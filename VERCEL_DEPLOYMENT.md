# 🚀 Vercel Deployment Guide

This guide will help you deploy your Enhanced Template Library to Vercel with proper environment variable configuration.

## 📋 Prerequisites

1. **Vercel Account**: Sign up at [vercel.com](https://vercel.com)
2. **Google Gemini API Key**: Get one from [Google AI Studio](https://makersuite.google.com/app/apikey)
3. **GitHub Repository**: Your code should be in a GitHub repository

## 🔧 Environment Variables Setup

### Step 1: Get Your Google Gemini API Key

1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Click "Create API Key"
3. Copy the generated API key (starts with `AIza...`)

### Step 2: Configure Environment Variables in Vercel

#### Method A: Vercel Dashboard (Recommended)

1. Go to [vercel.com](https://vercel.com) and log in
2. Select your project (or import from GitHub)
3. Go to **Settings** → **Environment Variables**
4. Add these variables one by one:

**Required Variables:**
```
Name: VITE_GEMINI_API_KEY
Value: [Your actual Gemini API key]
Environment: Production, Preview, Development
```

```
Name: VITE_AI_SERVICE_ENABLED
Value: true
Environment: Production, Preview, Development
```

```
Name: NODE_ENV
Value: production
Environment: Production
```

```
Name: VITE_APP_ENV
Value: production
Environment: Production
```

**Optional Variables:**
```
Name: VITE_AI_MAX_RETRIES
Value: 3
Environment: Production, Preview, Development
```

```
Name: VITE_AI_TIMEOUT
Value: 30000
Environment: Production, Preview, Development
```

```
Name: VITE_ENABLE_ANALYTICS
Value: true
Environment: Production, Preview, Development
```

#### Method B: Vercel CLI

```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Set environment variables
vercel env add VITE_GEMINI_API_KEY production
# Enter your API key when prompted

vercel env add VITE_AI_SERVICE_ENABLED production
# Enter: true

vercel env add NODE_ENV production
# Enter: production

vercel env add VITE_APP_ENV production
# Enter: production
```

## 🚀 Deployment Steps

### Option 1: Deploy via Vercel Dashboard

1. Go to [vercel.com/new](https://vercel.com/new)
2. Import your GitHub repository
3. Configure project settings:
   - **Framework Preset**: Vite
   - **Build Command**: `npm run build`
   - **Output Directory**: `dist`
   - **Install Command**: `npm install`
4. Add environment variables (see above)
5. Click **Deploy**

### Option 2: Deploy via CLI

```bash
# Install dependencies
npm install

# Build the project locally (optional test)
npm run build

# Deploy to Vercel
vercel --prod

# Follow the prompts to configure your project
```

### Option 3: Deploy via npm script

```bash
# Use the predefined deploy script
npm run deploy
```

## 🔍 Verification

After deployment, verify your app works correctly:

1. **Visit your deployed URL** (e.g., `https://your-app-name.vercel.app`)
2. **Test AI Generation**:
   - Go to AI Generation tab
   - Try generating a simple React project
   - Verify the AI responds correctly
3. **Check Console**: Open browser dev tools and check for any errors

## 🐛 Troubleshooting

### Common Issues:

#### 1. AI Generation Not Working
**Symptoms**: "AI service unavailable" or API errors
**Solutions**:
- Verify `VITE_GEMINI_API_KEY` is set correctly in Vercel
- Check API key is valid in Google AI Studio
- Ensure `VITE_AI_SERVICE_ENABLED=true`

#### 2. Build Failures
**Symptoms**: Deployment fails during build
**Solutions**:
- Check all environment variables are set
- Verify `NODE_ENV=production` is set
- Check build logs in Vercel dashboard

#### 3. Environment Variables Not Loading
**Symptoms**: App works locally but not on Vercel
**Solutions**:
- Ensure variables start with `VITE_` prefix
- Check variables are set for correct environment (Production/Preview)
- Redeploy after adding variables

### Debug Commands:

```bash
# Check environment variables locally
npm run dev
# Open browser console and check for config debug logs

# Test build locally
npm run build
npm run preview

# Check Vercel deployment logs
vercel logs [deployment-url]
```

## 📱 Custom Domain (Optional)

To add a custom domain:

1. Go to Vercel Dashboard → Your Project → Settings → Domains
2. Add your domain name
3. Configure DNS records as instructed
4. Update `VITE_APP_URL` environment variable to your custom domain

## 🔄 Automatic Deployments

Vercel automatically deploys when you push to your main branch. To configure:

1. Go to Settings → Git
2. Configure production branch (usually `main` or `master`)
3. Enable automatic deployments from GitHub

## 📊 Monitoring

Monitor your deployment:

1. **Vercel Analytics**: Enable in project settings
2. **Function Logs**: Check serverless function performance
3. **Build Logs**: Monitor deployment success/failures

## 🎯 Production Checklist

Before going live:

- [ ] All environment variables configured
- [ ] AI generation tested and working
- [ ] Custom domain configured (if needed)
- [ ] Analytics enabled
- [ ] Error tracking configured
- [ ] Performance optimized
- [ ] SEO meta tags added
- [ ] Favicon and app icons set

## 🔗 Useful Links

- [Vercel Documentation](https://vercel.com/docs)
- [Google AI Studio](https://makersuite.google.com/app/apikey)
- [Vite Environment Variables](https://vitejs.dev/guide/env-and-mode.html)
- [Your App Repository](https://github.com/your-username/your-repo)

---

**🎉 Your Enhanced Template Library is now ready for production on Vercel!**
