import React, { useEffect, useState } from 'react';
import { FolderTree, Code, LayoutGrid } from 'lucide-react';
import { useModeContext } from '../contexts/ModeContext';

const Header = () => {
  const { mode, setMode } = useModeContext();
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 10);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <header
      className={`sticky top-0 z-50 transition-all duration-300 ${scrolled ? 'bg-blue-900/60 backdrop-blur-sm shadow-md' : 'bg-gradient-to-r from-blue-900 to-blue-800'
        } text-white`}
    >
      <div className="max-w-6xl mx-auto px-4 py-3 flex items-center justify-between">
        {/* Logo */}
        <div className="flex items-center space-x-2 transform transition-transform hover:scale-95">
          <FolderTree size={28} />
          <h1 className="text-2xl font-extrabold tracking-tight">ProjectForge</h1>
        </div>

        {/* Navbar links */}
        <nav className="flex space-x-4">
          <button
            onClick={() => setMode('stack')}
            className={`flex items-center px-4 py-2 rounded-md transition-all ${mode === 'stack'
              ? 'bg-white text-blue-900 shadow'
              : 'text-blue-100 hover:bg-blue-700/50'
              }`}
          >
            <LayoutGrid size={18} className="mr-2" />
            <span>Template</span>
          </button>

          <button
            onClick={() => setMode('custom')}
            className={`flex items-center px-4 py-2 rounded-md transition-all ${mode === 'custom'
              ? 'bg-white text-blue-900 shadow'
              : 'text-blue-100 hover:bg-blue-700/50'
              }`}
          >
            <Code size={18} className="mr-2" />
            <span>Custom</span>
          </button>
        </nav>
      </div>
    </header>
  );
};

export default Header;
