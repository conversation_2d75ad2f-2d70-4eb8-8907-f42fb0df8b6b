// Test utility functions (no React components)
import { vi } from 'vitest'
import { useAppStore } from '../store/useAppStore'

// Mock the store for testing
export const createMockStore = (initialState: Partial<ReturnType<typeof useAppStore.getState>> = {}) => {
  const defaultState = {
    currentProject: null,
    isGenerating: false,
    mode: 'template' as const,
    history: [],
    favorites: [],
    customTemplates: [],
    sidebarOpen: true,
    activeTab: 'templates',
    searchQuery: '',
    selectedCategory: 'all',
    selectedTags: [],
    settings: {
      theme: 'light',
      autoSave: true,
      showPreview: true,
      enableAnalytics: true,
      language: 'en',
    },
    analytics: {
      projectsGenerated: 0,
      templatesUsed: {},
      featuresUsed: {},
      lastActive: new Date().toISOString(),
    },
  }

  return {
    ...defaultState,
    ...initialState,
    // Mock functions
    setCurrentProject: vi.fn(),
    setIsGenerating: vi.fn(),
    setMode: vi.fn(),
    addToHistory: vi.fn(),
    removeFromHistory: vi.fn(),
    clearHistory: vi.fn(),
    addToFavorites: vi.fn(),
    removeFromFavorites: vi.fn(),
    addCustomTemplate: vi.fn(),
    removeCustomTemplate: vi.fn(),
    setSidebarOpen: vi.fn(),
    setActiveTab: vi.fn(),
    setSearchQuery: vi.fn(),
    setSelectedCategory: vi.fn(),
    updateSettings: vi.fn(),
    incrementProjectsGenerated: vi.fn(),
    incrementTemplateUsed: vi.fn(),
    incrementFeatureUsed: vi.fn(),
    reset: vi.fn(),
  }
}

// Custom render function is now in ./render.tsx

// Test utilities
export const mockFileNode = (name: string, type: 'file' | 'directory' = 'file', children?: Array<{ name: string; type: string; children?: unknown[] }>) => ({
  name,
  type,
  children,
  content: type === 'file' ? `// ${name}\n// Generated content` : undefined,
})

export const mockProject = (overrides = {}) => ({
  id: 'test-project-1',
  name: 'Test Project',
  description: 'A test project for unit testing',
  type: 'react',
  structure: mockFileNode('src', 'directory', [
    mockFileNode('App.tsx'),
    mockFileNode('index.tsx'),
  ]),
  tags: ['react', 'typescript'],
  difficulty: 'Beginner' as const,
  estimatedTime: '30 minutes',
  features: ['TypeScript', 'React'],
  downloads: 1000,
  ...overrides,
})

export const mockTemplate = (overrides = {}) => ({
  id: 'test-template-1',
  name: 'Test Template',
  description: 'A test template',
  category: 'frontend',
  tags: ['react'],
  difficulty: 'Beginner' as const,
  structure: mockFileNode('src', 'directory'),
  downloads: 500,
  ...overrides,
})

// Mock API responses
export const mockApiResponse = <T,>(data: T, delay = 0): Promise<T> => {
  return new Promise((resolve) => {
    setTimeout(() => resolve(data), delay)
  })
}

// Mock browser APIs
export const mockLocalStorage = () => {
  const store: Record<string, string> = {}
  return {
    getItem: vi.fn((key: string) => store[key] || null),
    setItem: vi.fn((key: string, value: string) => {
      store[key] = value
    }),
    removeItem: vi.fn((key: string) => {
      delete store[key]
    }),
    clear: vi.fn(() => {
      Object.keys(store).forEach(key => delete store[key])
    }),
  }
}

// Mock intersection observer
export const mockIntersectionObserver = () => {
  const mockIntersectionObserver = vi.fn()
  mockIntersectionObserver.mockReturnValue({
    observe: () => null,
    unobserve: () => null,
    disconnect: () => null,
  })
  window.IntersectionObserver = mockIntersectionObserver
  window.IntersectionObserverEntry = vi.fn()
}

// Mock resize observer
export const mockResizeObserver = () => {
  const mockResizeObserver = vi.fn()
  mockResizeObserver.mockReturnValue({
    observe: () => null,
    unobserve: () => null,
    disconnect: () => null,
  })
  window.ResizeObserver = mockResizeObserver
  window.ResizeObserverEntry = vi.fn()
}

// Test data generators
export const generateMockProjects = (count: number) => {
  return Array.from({ length: count }, (_, i) => mockProject({
    id: `project-${i + 1}`,
    name: `Project ${i + 1}`,
  }))
}

export const generateMockTemplates = (count: number) => {
  return Array.from({ length: count }, (_, i) => mockTemplate({
    id: `template-${i + 1}`,
    name: `Template ${i + 1}`,
  }))
}

// Mock performance API
export const mockPerformance = () => {
  const mockNow = vi.fn(() => Date.now())
  Object.defineProperty(window, 'performance', {
    value: {
      now: mockNow,
      mark: vi.fn(),
      measure: vi.fn(),
      getEntriesByName: vi.fn(() => []),
      getEntriesByType: vi.fn(() => []),
    },
    writable: true,
  })
}

// Mock fetch API
export const mockFetch = (response: unknown, ok = true) => {
  return vi.fn(() =>
    Promise.resolve({
      ok,
      json: () => Promise.resolve(response),
      text: () => Promise.resolve(JSON.stringify(response)),
    })
  )
}

// Mock zustand store
export const mockZustandStore = <T,>(initialState: T) => {
  let state = initialState
  
  return {
    getState: () => state,
    setState: (newState: Partial<T>) => {
      state = { ...state, ...newState }
    },
    subscribe: vi.fn(),
    destroy: vi.fn(),
  }
}
