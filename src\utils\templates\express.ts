import { FileNode } from '../../types';

export function getExpressTemplate(): FileNode[] {
  return [
    {
      name: 'src',
      type: 'directory',
      children: [
        {
          name: 'index.js',
          type: 'file',
          content: `const express = require('express');
const path = require('path');
const logger = require('morgan');
const cors = require('cors');
require('dotenv').config();

// Import routes
const apiRoutes = require('./routes/api');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(logger('dev'));
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// Static files
app.use(express.static(path.join(__dirname, 'public')));

// Routes
app.use('/api', apiRoutes);

// Error handler
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ error: 'Not Found' });
});

// Start server
app.listen(PORT, () => {
  console.log(\`Server running on port \${PORT}\`);
});

module.exports = app;`
        },
        {
          name: 'routes',
          type: 'directory',
          children: [
            {
              name: 'api.js',
              type: 'file',
              content: `const express = require('express');
const router = express.Router();

// GET /api
router.get('/', (req, res) => {
  res.json({ message: 'API is working!' });
});

// GET /api/users
router.get('/users', (req, res) => {
  // Example response
  const users = [
    { id: 1, name: 'John Doe' },
    { id: 2, name: 'Jane Smith' }
  ];
  res.json(users);
});

// POST /api/users
router.post('/users', (req, res) => {
  const { name } = req.body;
  
  if (!name) {
    return res.status(400).json({ error: 'Name is required' });
  }
  
  // In a real app, you'd save to a database
  res.status(201).json({ id: 3, name });
});

module.exports = router;`
            }
          ]
        },
        {
          name: 'controllers',
          type: 'directory',
          children: [
            {
              name: 'userController.js',
              type: 'file',
              content: `// Example controller for user-related operations

/**
 * Get all users
 */
const getAllUsers = (req, res) => {
  // Example data (in a real app, this would come from a database)
  const users = [
    { id: 1, name: 'John Doe', email: '<EMAIL>' },
    { id: 2, name: 'Jane Smith', email: '<EMAIL>' }
  ];
  
  res.json(users);
};

/**
 * Get user by ID
 */
const getUserById = (req, res) => {
  const userId = parseInt(req.params.id);
  
  // Example data (in a real app, this would come from a database)
  const user = { id: userId, name: 'John Doe', email: '<EMAIL>' };
  
  res.json(user);
};

/**
 * Create a new user
 */
const createUser = (req, res) => {
  const { name, email } = req.body;
  
  if (!name || !email) {
    return res.status(400).json({ error: 'Name and email are required' });
  }
  
  // In a real app, you'd save to a database
  const newUser = {
    id: 3, // would be generated by database
    name,
    email
  };
  
  res.status(201).json(newUser);
};

module.exports = {
  getAllUsers,
  getUserById,
  createUser
};`
            }
          ]
        },
        {
          name: 'middleware',
          type: 'directory',
          children: [
            {
              name: 'auth.js',
              type: 'file',
              content: `/**
 * Authentication middleware example
 */
const authMiddleware = (req, res, next) => {
  // Get auth token from header
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'Unauthorized - No token provided' });
  }
  
  const token = authHeader.split(' ')[1];
  
  // In a real app, you would verify the token (e.g., using JWT)
  // For this example, we'll just check if it exists
  if (token === 'invalid-token') {
    return res.status(401).json({ error: 'Unauthorized - Invalid token' });
  }
  
  // Add user info to request
  req.user = { id: 1, name: 'John Doe' };
  
  next();
};

module.exports = {
  authMiddleware
};`
            }
          ]
        },
        {
          name: 'public',
          type: 'directory',
          children: [
            {
              name: 'index.html',
              type: 'file',
              content: `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Express API</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      color: #333;
    }
    .endpoint {
      background: #f4f4f4;
      padding: 10px;
      margin-bottom: 10px;
      border-radius: 5px;
    }
    .method {
      font-weight: bold;
      display: inline-block;
      width: 60px;
    }
    .url {
      color: #0066cc;
    }
  </style>
</head>
<body>
  <h1>Express API Documentation</h1>
  
  <p>Welcome to the API. Here are the available endpoints:</p>
  
  <div class="endpoint">
    <span class="method">GET</span>
    <span class="url">/api</span> - Check if API is working
  </div>
  
  <div class="endpoint">
    <span class="method">GET</span>
    <span class="url">/api/users</span> - Get all users
  </div>
  
  <div class="endpoint">
    <span class="method">POST</span>
    <span class="url">/api/users</span> - Create a new user
  </div>
  
  <p>For more information, check out the README.md file.</p>
</body>
</html>`
            }
          ]
        }
      ]
    },
    {
      name: 'package.json',
      type: 'file',
      content: `{
  "name": "express-api",
  "version": "1.0.0",
  "description": "Express API server",
  "main": "src/index.js",
  "scripts": {
    "start": "node src/index.js",
    "dev": "nodemon src/index.js",
    "test": "jest"
  },
  "keywords": [
    "express",
    "api",
    "node"
  ],
  "author": "",
  "license": "ISC",
  "dependencies": {
    "cors": "^2.8.5",
    "dotenv": "^16.3.1",
    "express": "^4.18.2",
    "morgan": "^1.10.0"
  },
  "devDependencies": {
    "jest": "^29.7.0",
    "nodemon": "^3.0.1",
    "supertest": "^6.3.3"
  }
}`
    },
    {
      name: '.env.example',
      type: 'file',
      content: `# Server Configuration
PORT=3000
NODE_ENV=development

# JWT Secret (for authentication)
JWT_SECRET=your_jwt_secret_key_here

# Database Configuration (if using a database)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=mydatabase
DB_USER=user
DB_PASSWORD=password

# API Configuration
API_VERSION=1
API_PREFIX=/api`
    },
    {
      name: '.gitignore',
      type: 'file',
      content: `# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.idea/
.vscode/
*.sublime-workspace
*.sublime-project

# OS files
.DS_Store
Thumbs.db`
    },
    {
      name: 'README.md',
      type: 'file',
      content: `# Express API

A RESTful API built with Express.js.

## Installation

\`\`\`bash
npm install
\`\`\`

## Configuration

Copy the example environment file:

\`\`\`bash
cp .env.example .env
\`\`\`

Then edit the \`.env\` file with your configuration.

## Running the Application

### Development

\`\`\`bash
npm run dev
\`\`\`

### Production

\`\`\`bash
npm start
\`\`\`

## API Endpoints

- \`GET /api\`: Check if API is working
- \`GET /api/users\`: Get all users
- \`POST /api/users\`: Create a new user (requires name and email in request body)

## Testing

\`\`\`bash
npm test
\`\`\``
    }
  ];
}