import { useEffect, useRef, useCallback, useMemo } from 'react'
import { logger, PerformanceMonitor } from '../utils/logger'

// Hook for measuring component render performance
export const useRenderPerformance = (componentName: string) => {
  const renderCount = useRef(0)
  const mountTime = useRef<number>()

  useEffect(() => {
    mountTime.current = performance.now()
    logger.debug(`Component mounted: ${componentName}`, undefined, 'performance')

    return () => {
      if (mountTime.current) {
        const lifetime = performance.now() - mountTime.current
        logger.performance(`Component lifetime: ${componentName}`, lifetime)
      }
    }
  }, [componentName])

  useEffect(() => {
    renderCount.current += 1
    if (renderCount.current > 1) {
      logger.debug(`Component re-rendered: ${componentName} (${renderCount.current} times)`, undefined, 'performance')
    }
  })

  return {
    renderCount: renderCount.current,
    logRender: useCallback((reason?: string) => {
      logger.debug(`Render triggered: ${componentName}`, { reason, count: renderCount.current }, 'performance')
    }, [componentName])
  }
}

// Hook for debouncing expensive operations
export const useDebounce = <T extends (...args: unknown[]) => unknown>(
  callback: T,
  delay: number,
  deps: React.DependencyList = []
): T => {
  const timeoutRef = useRef<NodeJS.Timeout>()

  const debouncedCallback = useCallback((...args: Parameters<T>) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    timeoutRef.current = setTimeout(() => {
      callback(...args)
    }, delay)
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [callback, delay, ...deps]) as T

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  return debouncedCallback
}

// Hook for throttling operations
export const useThrottle = <T extends (...args: unknown[]) => unknown>(
  callback: T,
  limit: number,
  deps: React.DependencyList = []
): T => {
  const inThrottle = useRef(false)

  const throttledCallback = useCallback((...args: Parameters<T>) => {
    if (!inThrottle.current) {
      callback(...args)
      inThrottle.current = true
      setTimeout(() => {
        inThrottle.current = false
      }, limit)
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [callback, limit, ...deps]) as T

  return throttledCallback
}

// Hook for measuring async operations
export const useAsyncPerformance = () => {
  const measureAsync = useCallback(async <T>(
    operation: string,
    asyncFn: () => Promise<T>,
    context?: Record<string, unknown>
  ): Promise<T> => {
    return PerformanceMonitor.measureAsync(operation, asyncFn, context)
  }, [])

  return { measureAsync }
}

// Hook for lazy loading with intersection observer
export const useLazyLoad = (threshold = 0.1) => {
  const elementRef = useRef<HTMLElement>(null)
  const isVisible = useRef(false)
  const hasLoaded = useRef(false)

  useEffect(() => {
    const element = elementRef.current
    if (!element || hasLoaded.current) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasLoaded.current) {
          isVisible.current = true
          hasLoaded.current = true
          logger.debug('Lazy load triggered', { element: element.tagName }, 'performance')
          observer.disconnect()
        }
      },
      { threshold }
    )

    observer.observe(element)

    return () => {
      observer.disconnect()
    }
  }, [threshold])

  return {
    ref: elementRef,
    isVisible: isVisible.current,
    hasLoaded: hasLoaded.current
  }
}

// Hook for memoizing expensive calculations
export const useExpensiveMemo = <T>(
  factory: () => T,
  deps: React.DependencyList,
  debugName?: string
): T => {
  return useMemo(() => {
    const startTime = performance.now()
    const result = factory()
    const duration = performance.now() - startTime
    
    if (debugName && duration > 10) { // Log if calculation takes more than 10ms
      logger.performance(`Expensive memo: ${debugName}`, duration)
    }
    
    return result
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, deps)
}

// Hook for monitoring memory usage
export const useMemoryMonitor = (componentName: string) => {
  useEffect(() => {
    if ('memory' in performance) {
      const memory = (performance as { memory?: { usedJSHeapSize: number; totalJSHeapSize: number; jsHeapSizeLimit: number } }).memory
      if (memory) {
        logger.debug(`Memory usage for ${componentName}`, {
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize,
          limit: memory.jsHeapSizeLimit
        }, 'performance')
      }
    }
  }, [componentName])
}

// Hook for tracking user interactions
export const useUserTracking = () => {
  const trackClick = useCallback((element: string, data?: Record<string, unknown>) => {
    logger.userAction(`Click: ${element}`, data)
  }, [])

  const trackFormSubmit = useCallback((formName: string, data?: Record<string, unknown>) => {
    logger.userAction(`Form submit: ${formName}`, data)
  }, [])

  const trackPageView = useCallback((pageName: string) => {
    logger.userAction(`Page view: ${pageName}`, {
      url: window.location.href,
      timestamp: Date.now()
    })
  }, [])

  const trackFeatureUsage = useCallback((feature: string, data?: Record<string, unknown>) => {
    logger.userAction(`Feature used: ${feature}`, data)
  }, [])

  return {
    trackClick,
    trackFormSubmit,
    trackPageView,
    trackFeatureUsage
  }
}

// Hook for error boundaries in functional components
export const useErrorHandler = () => {
  const handleError = useCallback((error: Error, errorInfo?: Record<string, unknown>) => {
    logger.error('Component error caught', {
      message: error.message,
      stack: error.stack,
      errorInfo,
      url: window.location.href
    }, 'error-boundary')
  }, [])

  const handleAsyncError = useCallback((error: Error, context?: string) => {
    logger.error(`Async error in ${context || 'unknown context'}`, {
      message: error.message,
      stack: error.stack,
      url: window.location.href
    }, 'async-error')
  }, [])

  return {
    handleError,
    handleAsyncError
  }
}

// Hook for monitoring network requests
export const useNetworkMonitor = () => {
  const trackRequest = useCallback((url: string, method: string, startTime: number) => {
    const duration = performance.now() - startTime
    logger.performance(`Network request: ${method} ${url}`, duration, {
      url,
      method,
      timestamp: Date.now()
    })
  }, [])

  const trackRequestError = useCallback((url: string, method: string, error: Error | string | unknown) => {
    logger.error(`Network request failed: ${method} ${url}`, {
      url,
      method,
      error: error instanceof Error ? error.message : String(error),
      timestamp: Date.now()
    }, 'network-error')
  }, [])

  return {
    trackRequest,
    trackRequestError
  }
}

// Hook for bundle size monitoring
export const useBundleMonitor = () => {
  useEffect(() => {
    // Monitor when new chunks are loaded
    const originalImport = window.__webpack_require__?.cache || {}
    
    logger.info('Bundle monitoring initialized', {
      chunksLoaded: Object.keys(originalImport).length,
      timestamp: Date.now()
    }, 'bundle-monitor')
  }, [])
}

// Hook for viewport monitoring
export const useViewportMonitor = () => {
  useEffect(() => {
    const logViewport = () => {
      logger.debug('Viewport changed', {
        width: window.innerWidth,
        height: window.innerHeight,
        devicePixelRatio: window.devicePixelRatio,
        orientation: screen.orientation?.type
      }, 'viewport')
    }

    logViewport() // Log initial viewport
    
    window.addEventListener('resize', logViewport)
    window.addEventListener('orientationchange', logViewport)

    return () => {
      window.removeEventListener('resize', logViewport)
      window.removeEventListener('orientationchange', logViewport)
    }
  }, [])
}

// Hook for Web Vitals monitoring
export const useWebVitals = () => {
  useEffect(() => {
    // This would integrate with web-vitals library in a real app
    // import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals'
    
    const logWebVital = (name: string, value: number) => {
      logger.performance(`Web Vital: ${name}`, value, {
        metric: name,
        value,
        timestamp: Date.now()
      })
    }

    // Mock web vitals for demonstration
    setTimeout(() => {
      logWebVital('FCP', Math.random() * 2000)
      logWebVital('LCP', Math.random() * 3000)
      logWebVital('CLS', Math.random() * 0.1)
    }, 1000)
  }, [])
}
