import { FileNode } from '../../types';

export function getNodeTemplate(): FileNode[] {
  return [
    {
      name: 'src',
      type: 'directory',
      children: [
        {
          name: 'index.js',
          type: 'file',
          content: `const http = require('http');

const hostname = '127.0.0.1';
const port = 3000;

const server = http.createServer((req, res) => {
  res.statusCode = 200;
  res.setHeader('Content-Type', 'text/plain');
  res.end('Hello World');
});

server.listen(port, hostname, () => {
  console.log(\`Server running at http://\${hostname}:\${port}/\`);
});`
        },
        {
          name: 'utils',
          type: 'directory',
          children: [
            {
              name: 'logger.js',
              type: 'file',
              content: `class Logger {
  constructor(prefix = '') {
    this.prefix = prefix;
  }

  log(message) {
    console.log(\`[\${this.prefix}] \${message}\`);
  }

  info(message) {
    console.info(\`[\${this.prefix}] [INFO] \${message}\`);
  }

  error(message) {
    console.error(\`[\${this.prefix}] [ERROR] \${message}\`);
  }

  warn(message) {
    console.warn(\`[\${this.prefix}] [WARN] \${message}\`);
  }
}

module.exports = Logger;`
            }
          ]
        }
      ]
    },
    {
      name: 'package.json',
      type: 'file',
      content: `{
  "name": "node-app",
  "version": "1.0.0",
  "description": "A simple Node.js application",
  "main": "src/index.js",
  "scripts": {
    "start": "node src/index.js",
    "dev": "nodemon src/index.js",
    "test": "jest"
  },
  "keywords": [
    "node",
    "javascript"
  ],
  "author": "",
  "license": "ISC",
  "dependencies": {
    "dotenv": "^16.3.1"
  },
  "devDependencies": {
    "jest": "^29.7.0",
    "nodemon": "^3.0.1"
  }
}`
    },
    {
      name: '.env.example',
      type: 'file',
      content: `# Server Configuration
PORT=3000
NODE_ENV=development

# Application Settings
APP_NAME=NodeApp
APP_VERSION=1.0.0

# Add your environment variables here`
    },
    {
      name: '.gitignore',
      type: 'file',
      content: `# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.idea/
.vscode/
*.sublime-workspace
*.sublime-project

# OS files
.DS_Store
Thumbs.db`
    },
    {
      name: 'README.md',
      type: 'file',
      content: `# Node.js Application

A simple Node.js application.

## Installation

\`\`\`bash
npm install
\`\`\`

## Configuration

Copy the example environment file:

\`\`\`bash
cp .env.example .env
\`\`\`

Then edit the \`.env\` file with your configuration.

## Running the Application

### Development

\`\`\`bash
npm run dev
\`\`\`

### Production

\`\`\`bash
npm start
\`\`\`

## Running Tests

\`\`\`bash
npm test
\`\`\``
    }
  ];
}