#!/usr/bin/env node

/**
 * Test script to verify Google Gemini API connection
 * Run with: node scripts/test-gemini-api.js
 */

import { GoogleGenerativeAI } from '@google/generative-ai';
import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
const envPath = path.join(__dirname, '..', '.env.local');

// Check if .env.local exists
if (fs.existsSync(envPath)) {
  dotenv.config({ path: envPath });
} else {
  console.log('⚠️  .env.local file not found, checking process.env...');
}

const API_KEY = process.env.VITE_GEMINI_API_KEY;

console.log('🔍 Testing Google Gemini API Connection...\n');

// Check if API key is configured
if (!API_KEY) {
  console.error('❌ Error: VITE_GEMINI_API_KEY not found in environment variables');
  console.log('📝 Please check your .env.local file and ensure it contains:');
  console.log('   VITE_GEMINI_API_KEY=your_api_key_here\n');
  process.exit(1);
}

console.log('✅ API Key found:', API_KEY.substring(0, 10) + '...' + API_KEY.slice(-4));
console.log('📍 API Key length:', API_KEY.length);

// Validate API key format
if (!API_KEY.startsWith('AIza')) {
  console.warn('⚠️  Warning: API key doesn\'t start with "AIza" - this might not be a valid Gemini API key');
}

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(API_KEY);

async function testConnection() {
  try {
    console.log('\n🚀 Initializing Gemini AI...');

    // Get the model
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
    console.log('✅ Model initialized successfully');

    // Test with a simple prompt
    console.log('\n📤 Sending test prompt...');
    const prompt = 'Respond with exactly the word "CONNECTED" if you can read this message.';

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    console.log('📥 Response received:', text);

    // Check if response is valid
    if (text && text.trim().toUpperCase().includes('CONNECTED')) {
      console.log('\n🎉 SUCCESS: Gemini API is working correctly!');
      console.log('✅ AI service is available and responding');
      return true;
    } else {
      console.log('\n⚠️  WARNING: Unexpected response from API');
      console.log('Expected: "CONNECTED"');
      console.log('Received:', text);
      return false;
    }

  } catch (error) {
    console.error('\n❌ ERROR: Failed to connect to Gemini API');
    console.error('Error details:', error.message);

    // Provide specific error guidance
    if (error.message.includes('API_KEY_INVALID')) {
      console.log('\n💡 Troubleshooting:');
      console.log('   1. Check if your API key is correct');
      console.log('   2. Verify the API key is enabled in Google AI Studio');
      console.log('   3. Make sure you have proper permissions');
    } else if (error.message.includes('QUOTA_EXCEEDED') || error.message.includes('429') || error.message.includes('quota')) {
      console.log('\n🚨 QUOTA EXCEEDED - FREE TIER LIMITS REACHED');
      console.log('\n💡 Troubleshooting:');
      console.log('   1. You have exceeded your API quota');
      console.log('   2. Check your usage in Google AI Studio');
      console.log('   3. Wait for quota reset or upgrade your plan');
      console.log('\n📊 Free tier limits:');
      console.log('   • 15 requests per minute');
      console.log('   • 1,500 requests per day');
      console.log('   • Resets every 24 hours');
      console.log('\n⏰ Solutions:');
      console.log('   1. Wait 24 hours for daily quota reset');
      console.log('   2. Check quota at: https://makersuite.google.com/app/apikey');
      console.log('   3. Consider upgrading to paid tier');
      console.log('   4. Use a different Google account/API key');
    } else if (error.message.includes('PERMISSION_DENIED')) {
      console.log('\n💡 Troubleshooting:');
      console.log('   1. API key doesn\'t have permission for Gemini API');
      console.log('   2. Enable the Generative AI API in Google Cloud Console');
      console.log('   3. Check your API key permissions');
    } else if (error.message.includes('503') || error.message.includes('overloaded')) {
      console.log('\n⚠️  SERVICE TEMPORARILY OVERLOADED');
      console.log('\n💡 Troubleshooting:');
      console.log('   1. The Gemini service is temporarily overloaded');
      console.log('   2. This is common with free tier usage');
      console.log('   3. Try again in a few minutes');
      console.log('   4. Consider using during off-peak hours');
    } else {
      console.log('\n💡 Troubleshooting:');
      console.log('   1. Check your internet connection');
      console.log('   2. Verify the API key is correct');
      console.log('   3. Try again in a few minutes');
    }

    return false;
  }
}

async function testProjectGeneration() {
  try {
    console.log('\n🧪 Testing project generation...');
    
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
    
    const prompt = `Generate a simple JSON response for a React project structure. 
    Respond with only this JSON format:
    {
      "projectName": "test-react-app",
      "structure": {
        "name": "test-react-app",
        "type": "directory",
        "children": [
          {
            "name": "src",
            "type": "directory",
            "children": [
              {
                "name": "App.tsx",
                "type": "file",
                "content": "import React from 'react'\\n\\nfunction App() {\\n  return <div>Hello World</div>\\n}\\n\\nexport default App"
              }
            ]
          }
        ]
      },
      "recommendations": ["Use TypeScript for better development experience"],
      "estimatedTime": "1-2 weeks",
      "difficulty": "Beginner"
    }`;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    console.log('📥 Project generation response length:', text.length);
    
    // Try to parse JSON
    try {
      const jsonMatch = text.match(/\{[\s\S]*\}/) || text.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        console.log('✅ JSON parsing successful');
        console.log('📋 Project name:', parsed.projectName);
        console.log('🏗️  Structure type:', parsed.structure?.type);
        console.log('💡 Recommendations count:', parsed.recommendations?.length || 0);
        return true;
      } else {
        console.log('⚠️  No valid JSON found in response');
        console.log('Raw response:', text.substring(0, 200) + '...');
        return false;
      }
    } catch (parseError) {
      console.log('❌ JSON parsing failed:', parseError.message);
      console.log('Raw response:', text.substring(0, 200) + '...');
      return false;
    }

  } catch (error) {
    console.error('❌ Project generation test failed:', error.message);
    return false;
  }
}

// Run tests
async function runTests() {
  console.log('=' .repeat(60));
  console.log('🧪 GEMINI API CONNECTION TEST');
  console.log('=' .repeat(60));

  const connectionTest = await testConnection();
  
  if (connectionTest) {
    const generationTest = await testProjectGeneration();
    
    console.log('\n' + '=' .repeat(60));
    console.log('📊 TEST RESULTS');
    console.log('=' .repeat(60));
    console.log('🔗 Connection Test:', connectionTest ? '✅ PASS' : '❌ FAIL');
    console.log('🏗️  Generation Test:', generationTest ? '✅ PASS' : '❌ FAIL');
    
    if (connectionTest && generationTest) {
      console.log('\n🎉 ALL TESTS PASSED! Your Gemini API is working correctly.');
      console.log('✅ You can now use AI features in ProjectForge.');
    } else {
      console.log('\n⚠️  Some tests failed. Please check the errors above.');
    }
  } else {
    console.log('\n❌ Connection test failed. Skipping generation test.');
    console.log('🔧 Please fix the connection issue first.');
  }

  console.log('\n📚 Useful links:');
  console.log('   • Google AI Studio: https://makersuite.google.com/app/apikey');
  console.log('   • Gemini API Docs: https://ai.google.dev/docs');
  console.log('   • Troubleshooting: https://ai.google.dev/docs/troubleshooting');
}

// Run the tests
runTests().catch(console.error);
