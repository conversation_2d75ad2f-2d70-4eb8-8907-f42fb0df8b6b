import { FileNode } from '../../types';

export function getFlaskTemplate(): FileNode[] {
  return [
    {
      name: 'app',
      type: 'directory',
      children: [
        {
          name: '__init__.py',
          type: 'file',
          content: `from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_cors import CORS
from config import Config

db = SQLAlchemy()
migrate = Migrate()

def create_app(config_class=Config):
    app = Flask(__name__)
    app.config.from_object(config_class)
    
    # Initialize extensions
    db.init_app(app)
    migrate.init_app(app, db)
    CORS(app)
    
    # Register blueprints
    from app.api import bp as api_bp
    app.register_blueprint(api_bp, url_prefix='/api')
    
    return app

from app import models`
        },
        {
          name: 'api',
          type: 'directory',
          children: [
            {
              name: '__init__.py',
              type: 'file',
              content: `from flask import Blueprint

bp = Blueprint('api', __name__)

from app.api import users, errors`
            },
            {
              name: 'users.py',
              type: 'file',
              content: `from flask import jsonify, request
from app import db
from app.api import bp
from app.models import User

@bp.route('/users', methods=['GET'])
def get_users():
    users = User.query.all()
    return jsonify([user.to_dict() for user in users])

@bp.route('/users/<int:id>', methods=['GET'])
def get_user(id):
    user = User.query.get_or_404(id)
    return jsonify(user.to_dict())

@bp.route('/users', methods=['POST'])
def create_user():
    data = request.get_json()
    user = User()
    user.from_dict(data)
    db.session.add(user)
    db.session.commit()
    return jsonify(user.to_dict()), 201`
            },
            {
              name: 'errors.py',
              type: 'file',
              content: `from flask import jsonify
from werkzeug.http import HTTP_STATUS_CODES
from app.api import bp

def error_response(status_code, message=None):
    payload = {'error': HTTP_STATUS_CODES.get(status_code, 'Unknown error')}
    if message:
        payload['message'] = message
    response = jsonify(payload)
    response.status_code = status_code
    return response

@bp.app_errorhandler(404)
def not_found_error(error):
    return error_response(404)

@bp.app_errorhandler(500)
def internal_error(error):
    return error_response(500)`
            }
          ]
        },
        {
          name: 'models',
          type: 'directory',
          children: [
            {
              name: '__init__.py',
              type: 'file',
              content: `from app.models.user import User`
            },
            {
              name: 'user.py',
              type: 'file',
              content: `from app import db
from werkzeug.security import generate_password_hash, check_password_hash

class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(128))
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
        
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
        
    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email
        }
        
    def from_dict(self, data):
        for field in ['username', 'email']:
            if field in data:
                setattr(self, field, data[field])
        if 'password' in data:
            self.set_password(data['password'])`
            }
          ]
        }
      ]
    },
    {
      name: 'config.py',
      type: 'file',
      content: `import os
from dotenv import load_dotenv

basedir = os.path.abspath(os.path.dirname(__file__))
load_dotenv(os.path.join(basedir, '.env'))

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'hard-to-guess-string'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'sqlite:///' + os.path.join(basedir, 'app.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False`
    },
    {
      name: 'requirements.txt',
      type: 'file',
      content: `flask==3.0.0
flask-sqlalchemy==3.1.1
flask-migrate==4.0.5
flask-cors==4.0.0
python-dotenv==1.0.0
werkzeug==3.0.1`
    },
    {
      name: '.env.example',
      type: 'file',
      content: `FLASK_APP=run.py
FLASK_ENV=development
SECRET_KEY=your-secret-key-here
DATABASE_URL=sqlite:///app.db`
    },
    {
      name: 'run.py',
      type: 'file',
      content: `from app import create_app, db
from app.models import User

app = create_app()

@app.shell_context_processor
def make_shell_context():
    return {'db': db, 'User': User}`
    }
  ];
}