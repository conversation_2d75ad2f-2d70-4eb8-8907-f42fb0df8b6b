# Contributing to ProjectForge

Thank you for your interest in contributing to ProjectForge! This document provides guidelines and information for contributors.

## 🚀 Getting Started

### Prerequisites
- Node.js 18 or higher
- npm or yarn
- Git

### Development Setup

1. **Fork the repository**
   ```bash
   # Fork on GitHub, then clone your fork
   git clone https://github.com/YOUR_USERNAME/projectforge.git
   cd projectforge
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Run tests**
   ```bash
   npm test
   ```

## 📋 Development Guidelines

### Code Style

We use ESLint and Prettier for code formatting. The configuration is already set up in the project.

```bash
# Check linting
npm run lint

# Auto-fix linting issues
npm run lint:fix

# Format code
npm run format
```

### Commit Messages

We follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

```
type(scope): description

[optional body]

[optional footer]
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

**Examples:**
```
feat(templates): add Vue 3 template with Composition API
fix(ai-generator): handle empty project descriptions
docs(readme): update installation instructions
test(components): add tests for Button component
```

### Branch Naming

Use descriptive branch names:
- `feature/template-search-filters`
- `fix/ai-generator-error-handling`
- `docs/api-documentation`
- `refactor/state-management`

## 🧪 Testing

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run tests with UI
npm run test:ui
```

### Writing Tests

- Write tests for all new features and bug fixes
- Use descriptive test names
- Follow the AAA pattern (Arrange, Act, Assert)
- Mock external dependencies

**Example test structure:**
```typescript
describe('ComponentName', () => {
  it('should render with default props', () => {
    // Arrange
    const props = { ... }
    
    // Act
    render(<ComponentName {...props} />)
    
    // Assert
    expect(screen.getByRole('button')).toBeInTheDocument()
  })
})
```

### Test Coverage

We aim for:
- **90%+ overall coverage**
- **100% coverage for utility functions**
- **80%+ coverage for components**

## 📁 Project Structure

```
src/
├── components/          # React components
│   ├── ui/             # Reusable UI components
│   ├── __tests__/      # Component tests
│   └── ...
├── hooks/              # Custom React hooks
├── store/              # Zustand state management
├── utils/              # Utility functions
├── services/           # API and external services
├── data/               # Static data and templates
├── lib/                # Core utilities
└── test/               # Test utilities and setup
```

## 🎯 Contributing Areas

### 1. Templates
Add new project templates to expand our library:

1. Create template structure in `src/data/templates.ts`
2. Add comprehensive metadata
3. Test the template generation
4. Update documentation

### 2. AI Generator
Improve the AI project generator:

1. Add new project types
2. Enhance generation logic
3. Improve recommendations
4. Add new technologies

### 3. UI/UX
Enhance the user interface:

1. Improve accessibility
2. Add new components
3. Enhance animations
4. Optimize performance

### 4. Testing
Expand test coverage:

1. Add component tests
2. Write integration tests
3. Add performance tests
4. Improve test utilities

## 🐛 Bug Reports

When reporting bugs, please include:

1. **Clear description** of the issue
2. **Steps to reproduce** the bug
3. **Expected behavior**
4. **Actual behavior**
5. **Environment details** (OS, browser, Node.js version)
6. **Screenshots** if applicable

Use our bug report template:

```markdown
## Bug Description
A clear description of the bug.

## Steps to Reproduce
1. Go to '...'
2. Click on '...'
3. See error

## Expected Behavior
What you expected to happen.

## Actual Behavior
What actually happened.

## Environment
- OS: [e.g., Windows 11, macOS 12]
- Browser: [e.g., Chrome 91, Firefox 89]
- Node.js: [e.g., 18.17.0]
- ProjectForge version: [e.g., 2.0.0]

## Additional Context
Any other context about the problem.
```

## 💡 Feature Requests

For feature requests, please:

1. **Check existing issues** to avoid duplicates
2. **Describe the feature** clearly
3. **Explain the use case** and benefits
4. **Provide examples** if possible

## 🔄 Pull Request Process

1. **Create a feature branch** from `main`
2. **Make your changes** following our guidelines
3. **Add tests** for new functionality
4. **Update documentation** if needed
5. **Ensure all tests pass**
6. **Submit a pull request**

### Pull Request Template

```markdown
## Description
Brief description of changes.

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Tests pass locally
- [ ] Added tests for new functionality
- [ ] Updated existing tests

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] No breaking changes (or documented)
```

## 📖 Documentation

When contributing documentation:

1. **Use clear, concise language**
2. **Include code examples**
3. **Add screenshots for UI changes**
4. **Update relevant sections**

## 🏆 Recognition

Contributors will be recognized in:
- README.md contributors section
- Release notes
- Project documentation

## 📞 Getting Help

If you need help:

1. **Check the documentation**
2. **Search existing issues**
3. **Join our Discord community**
4. **Create a discussion on GitHub**

## 📄 License

By contributing to ProjectForge, you agree that your contributions will be licensed under the MIT License.

---

Thank you for contributing to ProjectForge! 🚀
