# Deployment Guide 🚀

This guide covers deploying ProjectForge to Vercel with Google Gemini AI integration.

## 📋 Prerequisites

- [Vercel account](https://vercel.com/signup)
- [Google AI Studio account](https://makersuite.google.com/app/apikey) for Gemini API key
- Git repository (GitHub, GitLab, or Bitbucket)

## 🔑 Environment Variables Setup

### 1. Get Google Gemini API Key

1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with your Google account
3. Click "Create API Key"
4. Copy the generated API key (starts with `AIza...`)

### 2. Configure Environment Variables

You'll need to set these environment variables in Vercel:

| Variable | Value | Description |
|----------|-------|-------------|
| `VITE_GEMINI_API_KEY` | Your Gemini API key | Required for AI project generation |
| `VITE_ENABLE_AI_GENERATION` | `true` | Enable/disable AI features |
| `VITE_APP_ENV` | `production` | Application environment |

## 🚀 Deployment Methods

### Method 1: Vercel Dashboard (Recommended)

1. **Connect Repository**
   - Go to [Vercel Dashboard](https://vercel.com/dashboard)
   - Click "New Project"
   - Import your Git repository
   - Select the repository containing ProjectForge

2. **Configure Build Settings**
   - Framework Preset: `Vite`
   - Build Command: `npm run build`
   - Output Directory: `dist`
   - Install Command: `npm install`

3. **Set Environment Variables**
   - Go to Project Settings → Environment Variables
   - Add the variables listed above
   - Make sure to set them for all environments (Production, Preview, Development)

4. **Deploy**
   - Click "Deploy"
   - Wait for the build to complete
   - Your app will be available at `https://your-project.vercel.app`

### Method 2: Vercel CLI

1. **Install Vercel CLI**
   ```bash
   npm install -g vercel
   ```

2. **Login to Vercel**
   ```bash
   vercel login
   ```

3. **Deploy from Local**
   ```bash
   # First deployment
   vercel
   
   # Production deployment
   npm run deploy
   
   # Preview deployment
   npm run deploy:preview
   ```

4. **Set Environment Variables via CLI**
   ```bash
   vercel env add VITE_GEMINI_API_KEY
   vercel env add VITE_ENABLE_AI_GENERATION
   vercel env add VITE_APP_ENV
   ```

## ⚙️ Vercel Configuration

The project includes a `vercel.json` file with optimized settings:

```json
{
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "framework": "vite",
  "rewrites": [
    {
      "source": "/(.*)",
      "destination": "/index.html"
    }
  ]
}
```

### Key Features:
- **SPA Routing**: All routes redirect to `index.html` for client-side routing
- **CORS Headers**: Configured for API requests
- **Environment Variables**: Automatically injected during build
- **Optimized Caching**: Static assets cached efficiently

## 🔧 Build Optimization

### Bundle Analysis
```bash
# Analyze bundle size
npm run build
npx vite-bundle-analyzer dist
```

### Performance Tips
1. **Code Splitting**: Components are automatically code-split
2. **Tree Shaking**: Unused code is removed during build
3. **Asset Optimization**: Images and fonts are optimized
4. **Caching**: Static assets have long cache headers

## 🌍 Custom Domain Setup

1. **Add Domain in Vercel**
   - Go to Project Settings → Domains
   - Add your custom domain
   - Follow DNS configuration instructions

2. **SSL Certificate**
   - Vercel automatically provisions SSL certificates
   - HTTPS is enforced by default

## 📊 Monitoring & Analytics

### Vercel Analytics
```bash
npm install @vercel/analytics
```

Add to your `main.tsx`:
```typescript
import { Analytics } from '@vercel/analytics/react'

// Add <Analytics /> to your app
```

### Performance Monitoring
- **Core Web Vitals**: Automatically tracked
- **Real User Monitoring**: Available in Vercel dashboard
- **Error Tracking**: Built-in error reporting

## 🐛 Troubleshooting

### Common Issues

#### 1. Build Fails with TypeScript Errors
```bash
# Check TypeScript configuration
npx tsc --noEmit

# Fix type errors before deploying
npm run lint
```

#### 2. Environment Variables Not Working
- Ensure variables start with `VITE_`
- Check they're set in Vercel dashboard
- Redeploy after adding variables

#### 3. AI Service Not Working
- Verify `VITE_GEMINI_API_KEY` is set correctly
- Check API key permissions in Google AI Studio
- Monitor Vercel function logs for errors

#### 4. Routing Issues (404 on Refresh)
- Ensure `vercel.json` includes SPA rewrite rules
- Check that `index.html` is in the `dist` folder

### Debug Commands
```bash
# Local build test
npm run build && npm run preview

# Check environment variables
vercel env ls

# View deployment logs
vercel logs [deployment-url]
```

## 🔄 Continuous Deployment

### Automatic Deployments
- **Production**: Deploys automatically on `main` branch push
- **Preview**: Deploys automatically on pull requests
- **Branch Deployments**: Each branch gets a unique URL

### Deployment Hooks
```bash
# Add deployment webhook
vercel --prod --confirm
```

## 📈 Performance Optimization

### Lighthouse Scores Target
- **Performance**: 90+
- **Accessibility**: 95+
- **Best Practices**: 90+
- **SEO**: 90+

### Optimization Checklist
- [ ] Images optimized and properly sized
- [ ] Fonts preloaded
- [ ] Critical CSS inlined
- [ ] JavaScript code-split
- [ ] Service worker configured (if needed)

## 🔐 Security

### Security Headers
Configured in `vercel.json`:
- Content Security Policy
- X-Frame-Options
- X-Content-Type-Options
- Referrer Policy

### API Security
- Environment variables are server-side only
- No sensitive data in client bundle
- CORS properly configured

## 📞 Support

If you encounter issues:

1. **Check Vercel Status**: [status.vercel.com](https://status.vercel.com)
2. **Vercel Documentation**: [vercel.com/docs](https://vercel.com/docs)
3. **Community Support**: [github.com/vercel/vercel/discussions](https://github.com/vercel/vercel/discussions)

---

**Happy Deploying!** 🎉
