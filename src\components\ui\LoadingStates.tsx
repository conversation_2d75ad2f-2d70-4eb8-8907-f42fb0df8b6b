import React from 'react'
import { motion } from 'framer-motion'
import { Loader2, Sparkles, Code, Download } from 'lucide-react'
import { cn } from '../../lib/utils'

// Generic Loading Spinner
interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'md', 
  className 
}) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8',
  }

  return (
    <Loader2 
      className={cn('animate-spin', sizeClasses[size], className)} 
    />
  )
}

// Skeleton Components
interface SkeletonProps {
  className?: string
}

export const Skeleton: React.FC<SkeletonProps> = ({ className }) => (
  <div
    className={cn(
      'animate-pulse rounded-md bg-gray-200',
      className
    )}
  />
)

// Template Card Skeleton
export const TemplateCardSkeleton: React.FC = () => (
  <div className="bg-white rounded-lg border shadow-sm p-6 space-y-4">
    <div className="flex items-start justify-between">
      <div className="flex-1 space-y-2">
        <Skeleton className="h-5 w-3/4" />
        <Skeleton className="h-4 w-1/2" />
      </div>
      <Skeleton className="h-5 w-5 rounded-full" />
    </div>
    
    <Skeleton className="h-4 w-full" />
    <Skeleton className="h-4 w-2/3" />
    
    <div className="flex gap-2">
      <Skeleton className="h-6 w-16 rounded-full" />
      <Skeleton className="h-6 w-20 rounded-full" />
      <Skeleton className="h-6 w-14 rounded-full" />
    </div>
    
    <div className="flex justify-between items-center">
      <div className="flex gap-4">
        <Skeleton className="h-4 w-12" />
        <Skeleton className="h-4 w-16" />
      </div>
    </div>
    
    <Skeleton className="h-10 w-full rounded-md" />
  </div>
)

// File Tree Skeleton
export const FileTreeSkeleton: React.FC = () => (
  <div className="space-y-2">
    {[...Array(8)].map((_, i) => (
      <div key={i} className="flex items-center space-x-2" style={{ paddingLeft: `${(i % 3) * 16}px` }}>
        <Skeleton className="h-4 w-4" />
        <Skeleton className="h-4 w-4" />
        <Skeleton className={`h-4 ${i % 2 === 0 ? 'w-24' : 'w-32'}`} />
      </div>
    ))}
  </div>
)

// Analytics Chart Skeleton
export const ChartSkeleton: React.FC = () => (
  <div className="space-y-4">
    <Skeleton className="h-6 w-1/3" />
    <div className="h-64 bg-gray-100 rounded-lg flex items-end justify-center space-x-2 p-4">
      {[...Array(6)].map((_, i) => (
        <Skeleton 
          key={i} 
          className="w-8 bg-gray-200" 
          style={{ height: `${Math.random() * 150 + 50}px` }}
        />
      ))}
    </div>
  </div>
)

// Loading States with Messages
interface LoadingStateProps {
  type: 'generating' | 'downloading' | 'parsing' | 'loading'
  message?: string
  progress?: number
  className?: string
}

export const LoadingState: React.FC<LoadingStateProps> = ({ 
  type, 
  message, 
  progress,
  className 
}) => {
  const getIcon = () => {
    switch (type) {
      case 'generating':
        return <Sparkles className="h-8 w-8 text-purple-500" />
      case 'downloading':
        return <Download className="h-8 w-8 text-blue-500" />
      case 'parsing':
        return <Code className="h-8 w-8 text-green-500" />
      default:
        return <LoadingSpinner size="lg" className="text-gray-500" />
    }
  }

  const getDefaultMessage = () => {
    switch (type) {
      case 'generating':
        return 'Generating your project structure...'
      case 'downloading':
        return 'Preparing your download...'
      case 'parsing':
        return 'Parsing project structure...'
      default:
        return 'Loading...'
    }
  }

  return (
    <div className={cn('flex flex-col items-center justify-center p-8 text-center', className)}>
      <motion.div
        animate={{ 
          rotate: type === 'generating' ? 360 : 0,
          scale: [1, 1.1, 1]
        }}
        transition={{ 
          rotate: { duration: 2, repeat: Infinity, ease: 'linear' },
          scale: { duration: 1.5, repeat: Infinity }
        }}
        className="mb-4"
      >
        {getIcon()}
      </motion.div>
      
      <h3 className="text-lg font-medium text-gray-900 mb-2">
        {message || getDefaultMessage()}
      </h3>
      
      {progress !== undefined && (
        <div className="w-full max-w-xs">
          <div className="flex justify-between text-sm text-gray-600 mb-1">
            <span>Progress</span>
            <span>{Math.round(progress)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <motion.div
              className="bg-blue-500 h-2 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.3 }}
            />
          </div>
        </div>
      )}
      
      <div className="mt-4 flex space-x-1">
        {[...Array(3)].map((_, i) => (
          <motion.div
            key={i}
            className="w-2 h-2 bg-gray-400 rounded-full"
            animate={{ opacity: [0.3, 1, 0.3] }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              delay: i * 0.2,
            }}
          />
        ))}
      </div>
    </div>
  )
}

// Empty State Component
interface EmptyStateProps {
  icon?: React.ReactNode
  title: string
  description: string
  action?: React.ReactNode
  className?: string
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  icon,
  title,
  description,
  action,
  className
}) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    className={cn('flex flex-col items-center justify-center p-8 text-center', className)}
  >
    {icon && (
      <div className="mb-4 text-gray-400">
        {icon}
      </div>
    )}
    
    <h3 className="text-lg font-medium text-gray-900 mb-2">
      {title}
    </h3>
    
    <p className="text-gray-600 mb-6 max-w-sm">
      {description}
    </p>
    
    {action}
  </motion.div>
)

// Page Loading Component
export const PageLoading: React.FC = () => (
  <div className="min-h-screen bg-gray-50 flex items-center justify-center">
    <div className="text-center">
      <motion.div
        animate={{ rotate: 360 }}
        transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
        className="mb-4"
      >
        <Sparkles className="h-12 w-12 text-blue-500 mx-auto" />
      </motion.div>
      
      <h2 className="text-xl font-semibold text-gray-900 mb-2">
        Loading ProjectForge
      </h2>
      
      <p className="text-gray-600">
        Preparing your project generation experience...
      </p>
      
      <div className="mt-6 flex justify-center space-x-1">
        {[...Array(3)].map((_, i) => (
          <motion.div
            key={i}
            className="w-3 h-3 bg-blue-500 rounded-full"
            animate={{ 
              scale: [1, 1.2, 1],
              opacity: [0.7, 1, 0.7]
            }}
            transition={{
              duration: 1,
              repeat: Infinity,
              delay: i * 0.2,
            }}
          />
        ))}
      </div>
    </div>
  </div>
)

// Inline Loading Component
interface InlineLoadingProps {
  text?: string
  size?: 'sm' | 'md'
}

export const InlineLoading: React.FC<InlineLoadingProps> = ({ 
  text = 'Loading...', 
  size = 'sm' 
}) => (
  <div className="flex items-center space-x-2">
    <LoadingSpinner size={size} />
    <span className={cn(
      'text-gray-600',
      size === 'sm' ? 'text-sm' : 'text-base'
    )}>
      {text}
    </span>
  </div>
)
