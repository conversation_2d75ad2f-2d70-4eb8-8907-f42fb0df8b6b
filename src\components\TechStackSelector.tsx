import React, { useState } from 'react';
import { ArrowRight, PackageOpen } from 'lucide-react';
import { useProjectContext } from '../contexts/ProjectContext';
import { generateProjectStructure } from '../utils/projectGenerator';
import CustomStructureInput from './CustomStructureInput';

const frontendOptions = [
  { id: 'react', name: 'React', icon: '⚛️' },
  { id: 'vue', name: 'Vue.js', icon: '🟢' },
  { id: 'angular', name: 'Angular', icon: '🔺' },
  { id: 'svelte', name: '<PERSON>vel<PERSON>', icon: '🔥' },
  { id: 'next', name: 'Next.js', icon: '▲' }
];

const backendOptions = [
  { id: 'node', name: 'Node.js', icon: '💚' },
  { id: 'express', name: 'Express', icon: '🚂' },
  { id: 'django', name: 'Django', icon: '🐍' },
  { id: 'flask', name: 'Flask', icon: '🧪' },
  { id: 'rails', name: 'Ruby on Rails', icon: '💎' }
];

const databaseOptions = [
  { id: 'mongodb', name: 'MongoDB', icon: '🍃' },
  { id: 'postgresql', name: 'PostgreSQL', icon: '🐘' },
  { id: 'mysql', name: 'MySQL', icon: '🐬' },
  { id: 'sqlite', name: 'SQLite', icon: '📁' }
];

const TechStackSelector = () => {
  const { setProject, setIsGenerating } = useProjectContext();
  const [frontend, setFrontend] = useState('');
  const [backend, setBackend] = useState('');
  const [database, setDatabase] = useState('');
  const [name, setName] = useState('my-app');
  const [showStructurePage, setShowStructurePage] = useState(false);

  const handleGenerate = async () => {
    if (!frontend && !backend) {
      alert('Please select at least one technology');
      return;
    }

    setIsGenerating(true);

    try {
      setTimeout(() => {
        const structure = generateProjectStructure({
          name,
          frontend,
          backend,
          database
        });

        setProject({
          name,
          frontend,
          backend,
          database,
          structure
        });

        setIsGenerating(false);
        setShowStructurePage(true); // Show the structure page after generation
      }, 1000);
    } catch (error) {
      console.error('Error generating project:', error);
      setIsGenerating(false);
    }
  };

  const handleBack = () => {
    setShowStructurePage(false); // Go back to the tech stack selector
  };

  return (
    <div>
      {!showStructurePage ? (
        <div className="bg-white rounded-lg shadow-lg p-4 border border-gray-200 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-indigo-50 opacity-50"></div>
          <div className="relative z-10">
            <h2 className="text-xl font-semibold mb-4 text-gray-800 flex items-center">
              <PackageOpen className="mr-2" size={20} />
              Select Your Tech Stack
            </h2>

            <div className="space-y-4">
              <div>
                <label htmlFor="project-name" className="block text-sm font-medium text-gray-700 mb-1">
                  Project Name
                </label>
                <input
                  id="project-name"
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-shadow"
                  placeholder="my-awesome-app"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Frontend Framework
                </label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {frontendOptions.map((option) => (
                    <button
                      key={option.id}
                      onClick={() => setFrontend(option.id)}
                      className={`flex items-center justify-center p-3 rounded-md border transition-all transform hover:scale-105 ${frontend === option.id
                        ? 'border-blue-600 bg-blue-50 text-blue-700 shadow-md'
                        : 'border-gray-200 hover:bg-gray-50 hover:border-blue-300'
                        }`}
                    >
                      <span className="mr-2 text-xl">{option.icon}</span>
                      <span>{option.name}</span>
                    </button>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Backend Framework
                </label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {backendOptions.map((option) => (
                    <button
                      key={option.id}
                      onClick={() => setBackend(option.id)}
                      className={`flex items-center justify-center p-3 rounded-md border transition-all transform hover:scale-105 ${backend === option.id
                        ? 'border-blue-600 bg-blue-50 text-blue-700 shadow-md'
                        : 'border-gray-200 hover:bg-gray-50 hover:border-blue-300'
                        }`}
                    >
                      <span className="mr-2 text-xl">{option.icon}</span>
                      <span>{option.name}</span>
                    </button>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Database (Optional)
                </label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {databaseOptions.map((option) => (
                    <button
                      key={option.id}
                      onClick={() => setDatabase(option.id)}
                      className={`flex items-center justify-center p-3 rounded-md border transition-all transform hover:scale-105 ${database === option.id
                        ? 'border-blue-600 bg-blue-50 text-blue-700 shadow-md'
                        : 'border-gray-200 hover:bg-gray-50 hover:border-blue-300'
                        }`}
                    >
                      <span className="mr-2 text-xl">{option.icon}</span>
                      <span>{option.name}</span>
                    </button>
                  ))}
                </div>
              </div>

              <div className="pt-4">
                <button
                  onClick={handleGenerate}
                  className="w-full flex items-center justify-center px-4 py-3 border border-transparent rounded-md shadow-lg text-base font-medium text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all transform hover:scale-[1.02] hover:shadow-xl"
                >
                  Generate Project Structure
                  <ArrowRight className="ml-2" size={18} />
                </button>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <CustomStructureInput onBack={handleBack} />
      )}
    </div>
  );
};

export default TechStackSelector;
