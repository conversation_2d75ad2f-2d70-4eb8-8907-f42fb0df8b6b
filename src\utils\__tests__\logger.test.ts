import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { Logger, LogLevel, PerformanceMonitor, logger as defaultLogger } from '../logger'

// Create a test logger with DEBUG level enabled
const logger = new Logger({
  level: LogLevel.DEBUG,
  enableConsole: true,
  enableStorage: true,
  enableRemote: false,
})

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
})

// Mock console methods
const mockConsole = {
  log: vi.fn(),
  groupCollapsed: vi.fn(),
  groupEnd: vi.fn(),
}

Object.defineProperty(console, 'log', { value: mockConsole.log })
Object.defineProperty(console, 'groupCollapsed', { value: mockConsole.groupCollapsed })
Object.defineProperty(console, 'groupEnd', { value: mockConsole.groupEnd })

describe('Logger', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockLocalStorage.getItem.mockReturnValue('[]')
  })

  afterEach(() => {
    logger.clearLogs()
  })

  describe('Basic Logging', () => {
    it('logs debug messages', () => {
      logger.debug('Debug message', { test: 'data' })
      
      expect(mockConsole.groupCollapsed).toHaveBeenCalledWith(
        expect.stringContaining('[DEBUG]'),
        expect.any(String)
      )
    })

    it('logs info messages', () => {
      logger.info('Info message')
      
      expect(mockConsole.log).toHaveBeenCalledWith(
        expect.stringContaining('[INFO]'),
        expect.any(String)
      )
    })

    it('logs warning messages', () => {
      logger.warn('Warning message')
      
      expect(mockConsole.log).toHaveBeenCalledWith(
        expect.stringContaining('[WARN]'),
        expect.any(String)
      )
    })

    it('logs error messages', () => {
      logger.error('Error message')
      
      expect(mockConsole.log).toHaveBeenCalledWith(
        expect.stringContaining('[ERROR]'),
        expect.any(String)
      )
    })
  })

  describe('Log Storage', () => {
    it('stores logs in localStorage', () => {
      logger.info('Test message')
      
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'projectforge_logs',
        expect.stringContaining('Test message')
      )
    })

    it('retrieves logs from storage', () => {
      const testLogs = [
        {
          timestamp: '2024-01-01T00:00:00.000Z',
          level: LogLevel.INFO,
          message: 'Test message',
          sessionId: 'test-session',
        },
      ]
      
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(testLogs))
      
      const logs = logger.getLogs()
      expect(logs).toHaveLength(1)
      expect(logs[0].message).toBe('Test message')
    })

    it('clears logs', () => {
      logger.clearLogs()

      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('projectforge_logs')
      // After clearing, it logs "Logs cleared" message
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'projectforge_logs',
        expect.stringContaining('Logs cleared')
      )
    })
  })

  describe('Convenience Methods', () => {
    it('logs user actions', () => {
      logger.userAction('button_click', { buttonId: 'submit' })
      
      expect(mockConsole.groupCollapsed).toHaveBeenCalledWith(
        expect.stringContaining('User action: button_click'),
        expect.any(String)
      )
    })

    it('logs API calls', () => {
      logger.apiCall('/api/projects', 'GET', { params: { limit: 10 } })
      
      expect(mockConsole.groupCollapsed).toHaveBeenCalledWith(
        expect.stringContaining('API call: GET /api/projects'),
        expect.any(String)
      )
    })

    it('logs performance metrics', () => {
      logger.performance('component_render', 150, { component: 'TemplateCard' })
      
      expect(mockConsole.groupCollapsed).toHaveBeenCalledWith(
        expect.stringContaining('Performance: component_render took 150ms'),
        expect.any(String)
      )
    })
  })

  describe('Session Management', () => {
    it('generates unique session IDs', () => {
      const sessionInfo = logger.getSessionInfo()
      
      expect(sessionInfo.sessionId).toMatch(/^session_\d+_[a-z0-9]+$/)
    })

    it('sets and retrieves user ID', () => {
      logger.setUserId('user123')
      
      const sessionInfo = logger.getSessionInfo()
      expect(sessionInfo.userId).toBe('user123')
    })
  })

  describe('Log Filtering', () => {
    it('filters logs by level', () => {
      // Mock some logs in storage
      const testLogs = [
        { level: LogLevel.DEBUG, message: 'Debug', timestamp: '2024-01-01T00:00:00.000Z' },
        { level: LogLevel.INFO, message: 'Info', timestamp: '2024-01-01T00:00:00.000Z' },
        { level: LogLevel.ERROR, message: 'Error', timestamp: '2024-01-01T00:00:00.000Z' },
      ]
      
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(testLogs))
      
      const errorLogs = logger.getLogs(LogLevel.ERROR)
      expect(errorLogs).toHaveLength(1)
      expect(errorLogs[0].message).toBe('Error')
    })
  })

  describe('Export Functionality', () => {
    it('exports logs as JSON', () => {
      const testLogs = [
        { level: LogLevel.INFO, message: 'Test', timestamp: '2024-01-01T00:00:00.000Z' },
      ]
      
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(testLogs))
      
      const exported = logger.exportLogs()
      const parsed = JSON.parse(exported)
      
      expect(parsed).toHaveLength(1)
      expect(parsed[0].message).toBe('Test')
    })
  })
})

describe('PerformanceMonitor', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Configure default logger for PerformanceMonitor tests
    defaultLogger.setLogLevel(LogLevel.DEBUG)
    // Mock performance.now
    let time = 0
    vi.spyOn(performance, 'now').mockImplementation(() => time += 100)
  })

  it('measures operation duration', () => {
    PerformanceMonitor.start('test_operation')
    const duration = PerformanceMonitor.end('test_operation')

    expect(duration).toBe(100)
  })

  it('measures synchronous functions', () => {
    const testFn = vi.fn(() => 'result')

    const result = PerformanceMonitor.measure('sync_operation', testFn)

    expect(result).toBe('result')
    expect(testFn).toHaveBeenCalled()
  })

  it('measures asynchronous functions', async () => {
    const testFn = vi.fn(async () => 'async_result')

    const result = await PerformanceMonitor.measureAsync('async_operation', testFn)

    expect(result).toBe('async_result')
    expect(testFn).toHaveBeenCalled()
  })

  it('handles errors in measured functions', () => {
    const errorFn = vi.fn(() => {
      throw new Error('Test error')
    })
    
    expect(() => {
      PerformanceMonitor.measure('error_operation', errorFn)
    }).toThrow('Test error')
    
    expect(mockConsole.groupCollapsed).toHaveBeenCalledWith(
      expect.stringContaining('Performance: error_operation took'),
      expect.any(String)
    )
  })

  it('handles errors in async measured functions', async () => {
    const errorFn = vi.fn(async () => {
      throw new Error('Async test error')
    })
    
    await expect(
      PerformanceMonitor.measureAsync('async_error_operation', errorFn)
    ).rejects.toThrow('Async test error')
    
    expect(mockConsole.groupCollapsed).toHaveBeenCalledWith(
      expect.stringContaining('Performance: async_error_operation took'),
      expect.any(String)
    )
  })

  it('warns when timer not found', () => {
    const duration = PerformanceMonitor.end('nonexistent_operation')
    
    expect(duration).toBe(0)
    expect(mockConsole.log).toHaveBeenCalledWith(
      expect.stringContaining('[WARN]'),
      expect.any(String)
    )
  })
})
