import { FileNode } from '../types';

export function parseCustomStructure(input: string, projectName: string): FileNode {
  // Create root node with project name
  const rootNode: FileNode = {
    name: projectName,
    type: 'directory',
    children: []
  };
  
  // Normalize input by removing extra whitespace and empty lines
  const lines = input
    .split('\n')
    .map(line => line.trimEnd())
    .filter(line => line.trim() !== '');
  
  // Parse tree-style input (with ├── style markers)
  if (lines.some(line => line.includes('├──') || line.includes('│') || line.includes('└──'))) {
    parseTreeFormat(lines, rootNode);
  } else {
    // Parse simple format (with indentation or slashes)
    parseSimpleFormat(lines, rootNode);
  }
  
  return rootNode;
}

function parseTreeFormat(lines: string[], rootNode: FileNode): void {
  // Keep track of the current path in the tree
  const stack: { node: FileNode; level: number }[] = [{ node: rootNode, level: -1 }];
  
  lines.forEach(line => {
    // Skip the root node declaration if present (e.g., "my-project/")
    if (line.trim() === rootNode.name + '/' || line.trim() === rootNode.name) {
      return;
    }
    
    // Calculate indentation level and extract the item name
    const match = line.match(/^[\s│]*(?:├──|└──|─)\s*([^#]+)/);
    if (!match) return;
    
    // Remove any trailing comments and whitespace
    const item = match[1].trim();
    
    // Calculate level based on the number of spaces/vertical bars before the marker
    const level = Math.floor((line.indexOf('──') || line.indexOf('─')) / 2);
    
    // Pop stack until we find the parent node at the correct level
    while (stack.length > 1 && stack[stack.length - 1].level >= level) {
      stack.pop();
    }
    
    const parent = stack[stack.length - 1].node;
    if (!parent.children) {
      parent.children = [];
    }
    
    // Determine if it's a directory or file
    const isDirectory = item.endsWith('/') || !item.includes('.');
    const name = isDirectory ? item.replace(/\/$/, '') : item;
    
    const newNode: FileNode = {
      name,
      type: isDirectory ? 'directory' : 'file',
      children: isDirectory ? [] : undefined,
      content: !isDirectory ? generateDefaultContent(name) : undefined
    };
    
    parent.children.push(newNode);
    
    if (isDirectory) {
      stack.push({ node: newNode, level });
    }
  });
}

function parseSimpleFormat(lines: string[], rootNode: FileNode): void {
  lines.forEach(line => {
    const trimmedLine = line.trim();
    if (!trimmedLine) return;
    
    // Skip the root node declaration if present
    if (trimmedLine === rootNode.name + '/' || trimmedLine === rootNode.name) {
      return;
    }
    
    // Split path into segments and remove comments
    const path = trimmedLine
      .split('#')[0] // Remove comments
      .trim()
      .split('/')
      .filter(segment => segment.trim() !== '');
    
    if (path.length === 0) return;
    
    // Start from root node
    let currentNode = rootNode;
    
    // Process each segment except the last one
    for (let i = 0; i < path.length - 1; i++) {
      if (!currentNode.children) {
        currentNode.children = [];
      }
      
      // Look for existing directory node
      let nextNode = currentNode.children.find(
        child => child.name === path[i] && child.type === 'directory'
      );
      
      // Create directory if it doesn't exist
      if (!nextNode) {
        nextNode = {
          name: path[i],
          type: 'directory',
          children: []
        };
        currentNode.children.push(nextNode);
      }
      
      currentNode = nextNode;
    }
    
    // Process the last segment (file or directory)
    const lastSegment = path[path.length - 1];
    const isDirectory = lastSegment.endsWith('/') || !lastSegment.includes('.');
    const name = isDirectory ? lastSegment.replace(/\/$/, '') : lastSegment;
    
    if (!currentNode.children) {
      currentNode.children = [];
    }
    
    // Skip if already exists
    if (currentNode.children.some(child => child.name === name)) {
      return;
    }
    
    const newNode: FileNode = {
      name,
      type: isDirectory ? 'directory' : 'file',
      children: isDirectory ? [] : undefined,
      content: !isDirectory ? generateDefaultContent(name) : undefined
    };
    
    currentNode.children.push(newNode);
  });
}

function generateDefaultContent(filename: string): string | undefined {
  const ext = filename.split('.').pop() || '';
  
  switch (ext.toLowerCase()) {
    case 'js': {
      if (filename.includes('Controller')) {
        return `/**
 * ${filename}
 * Controller for handling ${filename.replace('Controller.js', '')} operations
 */

class ${getClassName(filename)} {
  async index(req, res) {
    try {
      res.json({ message: 'Success' });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async show(req, res) {
    try {
      const { id } = req.params;
      res.json({ id });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async create(req, res) {
    try {
      const data = req.body;
      res.status(201).json(data);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async update(req, res) {
    try {
      const { id } = req.params;
      const data = req.body;
      res.json({ id, ...data });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async delete(req, res) {
    try {
      const { id } = req.params;
      res.json({ message: 'Deleted successfully' });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
}

module.exports = new ${getClassName(filename)}();`;
      }
      
      if (filename.includes('Routes')) {
        return `const express = require('express');
const router = express.Router();
const controller = require('../controllers/${filename.replace('Routes.js', 'Controller.js')}');

router.get('/', controller.index);
router.get('/:id', controller.show);
router.post('/', controller.create);
router.put('/:id', controller.update);
router.delete('/:id', controller.delete);

module.exports = router;`;
      }
      
      if (filename.includes('Service')) {
        return `/**
 * ${filename}
 * Service for handling business logic
 */

class ${getClassName(filename)} {
  async process(data) {
    try {
      return { success: true, data };
    } catch (error) {
      throw new Error(\`Error in ${filename}: \${error.message}\`);
    }
  }
}

module.exports = new ${getClassName(filename)}();`;
      }
      
      if (filename === 'app.js') {
        return `const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
require('dotenv').config();

const app = express();

// Middleware
app.use(cors());
app.use(morgan('dev'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes
app.get('/', (req, res) => {
  res.json({ message: 'Welcome to the API' });
});

// Error handling
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

module.exports = app;`;
      }
      
      if (filename === 'server.js') {
        return `const app = require('./app');

const PORT = process.env.PORT || 3000;

app.listen(PORT, () => {
  console.log(\`Server is running on port \${PORT}\`);
});`;
      }
      
      // Default JavaScript file
      return `/**
 * ${filename}
 */

// Add your code here
`;
    }
    
    case 'json': {
      if (filename === 'package.json') {
        return `{
  "name": "server",
  "version": "1.0.0",
  "description": "",
  "main": "server.js",
  "scripts": {
    "start": "node server.js",
    "dev": "nodemon server.js",
    "test": "jest"
  },
  "keywords": [],
  "author": "",
  "license": "ISC",
  "dependencies": {
    "cors": "^2.8.5",
    "dotenv": "^16.0.3",
    "express": "^4.18.2",
    "morgan": "^1.10.0"
  },
  "devDependencies": {
    "jest": "^29.5.0",
    "nodemon": "^2.0.22"
  }
}`;
      }
      return '{}';
    }
    
    default:
      return `// ${filename}\n`;
  }
}

function getClassName(filename: string): string {
  return filename
    .split('.')
    .slice(0, -1)
    .join('')
    .replace(/[-_]([a-z])/g, (_, char) => char.toUpperCase())
    .replace(/^[a-z]/, char => char.toUpperCase());
}