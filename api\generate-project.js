// Vercel Serverless Function for AI Project Generation
import { GoogleGenerativeAI } from '@google/generative-ai';

// This runs on the server, so the API key is secure
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

export default async function handler(req, res) {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { prompt, options = {} } = req.body;

    if (!prompt) {
      return res.status(400).json({ error: 'Prompt is required' });
    }

    // Check if API key is configured
    if (!process.env.GEMINI_API_KEY) {
      return res.status(500).json({ error: 'AI service not configured' });
    }

    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

    // Enhanced prompt for better project generation
    const enhancedPrompt = `
Generate a detailed project structure for: ${prompt}

Requirements:
- Include package.json with appropriate dependencies
- Create a logical folder structure
- Add README.md with setup instructions
- Include basic configuration files
- ${options.includeTests ? 'Include test files and testing setup' : 'Skip test files'}
- ${options.includeDocumentation ? 'Include comprehensive documentation' : 'Include basic documentation'}

Format the response as a JSON object with:
{
  "projectName": "kebab-case-name",
  "description": "Brief description",
  "structure": {
    "name": "project-root",
    "type": "directory",
    "children": [...]
  },
  "recommendations": ["tip1", "tip2"],
  "estimatedTime": "X hours",
  "difficulty": "Beginner|Intermediate|Advanced"
}

Ensure all file contents are realistic and functional.
`;

    const result = await model.generateContent(enhancedPrompt);
    const response = await result.response;
    const text = response.text();

    // Try to parse JSON response
    let projectData;
    try {
      // Extract JSON from response (in case there's extra text)
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        projectData = JSON.parse(jsonMatch[0]);
      } else {
        throw new Error('No JSON found in response');
      }
    } catch (parseError) {
      // If JSON parsing fails, return a structured error
      return res.status(500).json({
        error: 'Failed to parse AI response',
        rawResponse: text.substring(0, 500) // First 500 chars for debugging
      });
    }

    // Add metadata
    projectData.aiProvider = 'Google Gemini';
    projectData.generatedAt = new Date().toISOString();

    return res.status(200).json(projectData);

  } catch (error) {
    console.error('AI Generation Error:', error);
    
    // Return appropriate error response
    if (error.message?.includes('API_KEY')) {
      return res.status(401).json({ error: 'Invalid API key' });
    } else if (error.message?.includes('quota')) {
      return res.status(429).json({ error: 'API quota exceeded' });
    } else {
      return res.status(500).json({ 
        error: 'AI service temporarily unavailable',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }
}
