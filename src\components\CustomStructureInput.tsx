import React, { useState } from 'react';
import { FileText, FolderTree } from 'lucide-react';
import { useProjectContext } from '../contexts/ProjectContext';
import { parseCustomStructure } from '../utils/structureParser';

// Example project structure
const example = `src/
├── components/
│   ├── Button.jsx
│   └── Header.jsx
├── pages/
│   ├── Home.jsx
│   └── About.jsx
├── utils/
│   └── helpers.js
├── App.jsx
└── index.jsx`;

// Define the types for props
interface CustomStructureInputProps {
  onBack: () => void; // Explicitly defining the type for the onBack prop
}

const CustomStructureInput: React.FC<CustomStructureInputProps> = ({ onBack }) => {
  const { setProject, setIsGenerating } = useProjectContext();
  const [input, setInput] = useState('');
  const [projectName, setProjectName] = useState('custom-project');

  // Function to handle structure generation
  const handleGenerate = () => {
    if (!input.trim()) {
      alert('Please enter a project structure');
      return;
    }

    setIsGenerating(true);

    try {
      setTimeout(() => {
        const structure = parseCustomStructure(input, projectName);

        setProject({
          name: projectName,
          frontend: 'custom',
          backend: 'custom',
          database: null,
          structure
        });

        setIsGenerating(false);
      }, 800);
    } catch (error) {
      console.error('Error parsing structure:', error);
      setIsGenerating(false);
      alert('Error parsing structure. Please check your input format.');
    }
  };

  // Function to load example structure
  const loadExample = () => {
    setInput(example);
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-4 border border-gray-200 relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-purple-50 to-blue-50 opacity-50"></div>
      <div className="relative z-10">
        {/* Back Button */}
        <button onClick={onBack} className="mb-4 text-sm text-blue-600 hover:text-blue-800 transition-colors">
          Back to Tech Stack Selection
        </button>

        <h2 className="text-xl font-semibold mb-4 text-gray-800 flex items-center">
          <FolderTree className="mr-2" size={20} />
          Custom Project Structure
        </h2>

        <div className="space-y-4">
          {/* Project Name Input */}
          <div>
            <label htmlFor="project-name" className="block text-sm font-medium text-gray-700 mb-1">
              Project Name
            </label>
            <input
              id="project-name"
              type="text"
              value={projectName}
              onChange={(e) => setProjectName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-shadow"
              placeholder="my-custom-project"
            />
          </div>

          {/* Project Structure Input */}
          <div>
            <div className="flex justify-between items-center mb-1">
              <label htmlFor="structure-input" className="block text-sm font-medium text-gray-700">
                Paste Your Project Structure
              </label>
              <button
                onClick={loadExample}
                className="text-sm text-purple-600 hover:text-purple-800 transition-colors"
              >
                Load Example
              </button>
            </div>
            <textarea
              id="structure-input"
              value={input}
              onChange={(e) => setInput(e.target.value)}
              className="w-full h-72 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 font-mono text-sm transition-shadow bg-gray-50"
              placeholder="Paste your folder structure here using folder/, file.js format or typical tree representation with ├── and │ characters"
              spellCheck="false"
            />
            <p className="mt-1 text-sm text-gray-500">
              Use a tree structure format with folders ending in <code>/</code> and files with appropriate extensions
            </p>
          </div>

          {/* Generate Button */}
          <div className="pt-2">
            <button
              onClick={handleGenerate}
              className="w-full flex items-center justify-center px-4 py-3 border border-transparent rounded-md shadow-lg text-base font-medium text-white bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all transform hover:scale-[1.02] hover:shadow-xl"
            >
              Parse Structure
              <FileText className="ml-2" size={18} />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomStructureInput;
