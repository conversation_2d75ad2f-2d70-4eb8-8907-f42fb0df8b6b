# AI Configuration (Server-side - SECURE)
GEMINI_API_KEY=your_gemini_api_key_here

# AI Configuration (Client-side - SAFE)
VITE_AI_SERVICE_ENABLED=true
VITE_AI_MAX_RETRIES=3
VITE_AI_TIMEOUT=30000

# Environment Configuration
NODE_ENV=production
VITE_APP_ENV=production
VITE_APP_URL=https://your-app-name.vercel.app
VITE_API_BASE_URL=https://your-app-name.vercel.app/api

# Analytics (Optional)
VITE_ENABLE_ANALYTICS=true
VITE_ANALYTICS_ID=your_analytics_id_here

# Error Tracking (Optional)
VITE_SENTRY_DSN=your_sentry_dsn_here

# Logging (Optional)
VITE_ENABLE_REMOTE_LOGGING=false
VITE_LOGGING_ENDPOINT=https://your-logging-service.com/api/logs
