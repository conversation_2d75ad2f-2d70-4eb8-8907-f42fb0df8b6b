import { StackSelection, FileNode } from '../types';
import { getReactTemplate } from './templates/react';
import { getVueTemplate } from './templates/vue';
import { getNextTemplate } from './templates/next';
import { getNodeTemplate } from './templates/node';
import { getExpressTemplate } from './templates/express';

export function generateProjectStructure(options: StackSelection): FileNode {
  const { name, frontend, backend, database } = options;
  
  // Create root node
  const rootNode: FileNode = {
    name,
    type: 'directory',
    children: []
  };
  
  // Generate frontend structure
  let frontendStructure: FileNode[] = [];
  if (frontend) {
    switch (frontend) {
      case 'react':
        frontendStructure = getReactTemplate();
        break;
      case 'vue':
        frontendStructure = getVueTemplate();
        break;
      case 'next':
        frontendStructure = getNextTemplate();
        break;
      // Add more frontend frameworks as needed
      default:
        frontendStructure = [];
    }
  }
  
  // Generate backend structure
  let backendStructure: FileNode[] = [];
  if (backend) {
    switch (backend) {
      case 'node':
        backendStructure = getNodeTemplate();
        break;
      case 'express':
        backendStructure = getExpressTemplate();
        break;
      // Add more backend frameworks as needed
      default:
        backendStructure = [];
    }
  }
  
  // Combine structures
  if (frontend && backend) {
    // If we have both, create a monorepo structure
    const frontendDir: FileNode = {
      name: 'frontend',
      type: 'directory',
      children: frontendStructure
    };
    
    const backendDir: FileNode = {
      name: 'backend',
      type: 'directory',
      children: backendStructure
    };
    
    rootNode.children = [
      frontendDir,
      backendDir,
      {
        name: 'package.json',
        type: 'file',
        content: JSON.stringify({
          name,
          version: '1.0.0',
          private: true,
          workspaces: ['frontend', 'backend'],
          scripts: {
            start: 'concurrently "npm run start:frontend" "npm run start:backend"',
            'start:frontend': 'cd frontend && npm start',
            'start:backend': 'cd backend && npm start',
            install: 'npm install && concurrently "cd frontend && npm install" "cd backend && npm install"'
          },
          devDependencies: {
            concurrently: '^8.2.2'
          }
        }, null, 2)
      },
      {
        name: 'README.md',
        type: 'file',
        content: `# ${name}\n\nA full-stack application using ${frontend} for the frontend and ${backend} for the backend.\n\n## Getting Started\n\n\`\`\`bash\nnpm install\nnpm start\n\`\`\``
      }
    ];
  } else if (frontend) {
    // Only frontend
    rootNode.children = frontendStructure;
  } else if (backend) {
    // Only backend
    rootNode.children = backendStructure;
  }
  
  // Add database connection if specified
  if (database && backend) {
    const dbConfigFile: FileNode = {
      name: 'database.js',
      type: 'file',
      content: getDatabaseConfig(database)
    };
    
    // Find the backend directory or use root if no separate frontend/backend
    if (rootNode.children?.find(child => child.name === 'backend')) {
      const backendDir = rootNode.children.find(child => child.name === 'backend');
      backendDir?.children?.push(dbConfigFile);
    } else {
      rootNode.children?.push(dbConfigFile);
    }
  }
  
  return rootNode;
}

function getDatabaseConfig(database: string): string {
  switch (database) {
    case 'mongodb':
      return `// MongoDB connection configuration
const mongoose = require('mongoose');

const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/myapp');
    console.log('MongoDB Connected');
  } catch (err) {
    console.error('Failed to connect to MongoDB', err);
    process.exit(1);
  }
};

module.exports = connectDB;`;
      
    case 'postgresql':
      return `// PostgreSQL connection configuration
const { Pool } = require('pg');

const pool = new Pool({
  user: process.env.PGUSER || 'postgres',
  host: process.env.PGHOST || 'localhost',
  database: process.env.PGDATABASE || 'myapp',
  password: process.env.PGPASSWORD || 'postgres',
  port: parseInt(process.env.PGPORT || '5432'),
});

module.exports = {
  query: (text, params) => pool.query(text, params),
};`;
      
    case 'mysql':
      return `// MySQL connection configuration
const mysql = require('mysql2/promise');

const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'myapp',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

module.exports = pool;`;
      
    default:
      return `// Database configuration for ${database}\n// Add your connection code here`;
  }
}