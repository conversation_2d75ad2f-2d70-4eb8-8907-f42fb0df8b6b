import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

export function generateId(): string {
  return Math.random().toString(36).substr(2, 9)
}

export function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

export function throttle<T extends (...args: unknown[]) => unknown>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

export function copyToClipboard(text: string): Promise<void> {
  if (navigator.clipboard && window.isSecureContext) {
    return navigator.clipboard.writeText(text)
  } else {
    // Fallback for older browsers
    const textArea = document.createElement('textarea')
    textArea.value = text
    textArea.style.position = 'fixed'
    textArea.style.left = '-999999px'
    textArea.style.top = '-999999px'
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()
    return new Promise((resolve, reject) => {
      if (document.execCommand('copy')) {
        textArea.remove()
        resolve()
      } else {
        textArea.remove()
        reject(new Error('Unable to copy to clipboard'))
      }
    })
  }
}

export function validateProjectName(name: string): { isValid: boolean; error?: string } {
  if (!name.trim()) {
    return { isValid: false, error: 'Project name is required' }
  }
  
  if (name.length < 2) {
    return { isValid: false, error: 'Project name must be at least 2 characters' }
  }
  
  if (name.length > 50) {
    return { isValid: false, error: 'Project name must be less than 50 characters' }
  }
  
  if (!/^[a-zA-Z0-9-_\s]+$/.test(name)) {
    return { isValid: false, error: 'Project name can only contain letters, numbers, hyphens, underscores, and spaces' }
  }
  
  return { isValid: true }
}

export function sanitizeFileName(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9-_\s]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '')
}

export function getFileIcon(fileName: string): string {
  const extension = fileName.split('.').pop()?.toLowerCase()
  
  const iconMap: Record<string, string> = {
    // Web
    'html': '🌐',
    'css': '🎨',
    'scss': '🎨',
    'sass': '🎨',
    'less': '🎨',
    'js': '📜',
    'jsx': '⚛️',
    'ts': '📘',
    'tsx': '⚛️',
    'vue': '💚',
    'svelte': '🔥',
    
    // Backend
    'py': '🐍',
    'rb': '💎',
    'php': '🐘',
    'java': '☕',
    'go': '🐹',
    'rs': '🦀',
    'cpp': '⚙️',
    'c': '⚙️',
    
    // Data
    'json': '📋',
    'xml': '📄',
    'yaml': '📝',
    'yml': '📝',
    'toml': '📝',
    'csv': '📊',
    
    // Config
    'env': '🔧',
    'config': '⚙️',
    'conf': '⚙️',
    'ini': '⚙️',
    
    // Documentation
    'md': '📖',
    'txt': '📄',
    'pdf': '📕',
    'doc': '📄',
    'docx': '📄',
    
    // Images
    'png': '🖼️',
    'jpg': '🖼️',
    'jpeg': '🖼️',
    'gif': '🖼️',
    'svg': '🎨',
    'ico': '🖼️',
    
    // Other
    'zip': '📦',
    'tar': '📦',
    'gz': '📦',
    'rar': '📦',
    'lock': '🔒',
    'log': '📋',
  }
  
  return iconMap[extension || ''] || '📄'
}
