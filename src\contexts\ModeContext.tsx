import React, { createContext, useState, ReactNode } from 'react';

type Mode = 'stack' | 'custom';

interface ModeContextType {
  mode: Mode;
  setMode: (mode: Mode) => void;
}

const ModeContext = createContext<ModeContextType | undefined>(undefined);

export const ModeProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [mode, setMode] = useState<Mode>('stack');

  return (
    <ModeContext.Provider value={{ mode, setMode }}>
      {children}
    </ModeContext.Provider>
  );
};

// Export hook separately to avoid Fast Refresh issues
// eslint-disable-next-line react-refresh/only-export-components
export { useModeContext } from '../hooks/useModeContext';