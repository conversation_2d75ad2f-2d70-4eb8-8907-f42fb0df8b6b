import J<PERSON><PERSON><PERSON> from 'jszip';
import { Project, FileNode } from '../types';

export async function generateZip(project: Project): Promise<void> {
  const zip = new JSZip();
  
  // Add files recursively
  function addToZip(node: FileNode, parentPath: string = '') {
    const path = `${parentPath}${node.name}`;
    
    if (node.type === 'directory') {
      // Create folder
      zip.folder(path);
      
      // Add children recursively
      if (node.children) {
        node.children.forEach(child => {
          addToZip(child, `${path}/`);
        });
      }
    } else {
      // Add file with content
      zip.file(path, node.content || '');
    }
  }
  
  // Start adding files from root
  if (project.structure) {
    addToZip(project.structure);
  }
  
  try {
    // Generate ZIP file
    const content = await zip.generateAsync({
      type: 'blob',
      compression: 'DEFLATE',
      compressionOptions: {
        level: 9
      }
    });
    
    // Create download link
    const url = URL.createObjectURL(content);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${project.name.toLowerCase().replace(/\s+/g, '-')}.zip`;
    
    // Trigger download
    document.body.appendChild(link);
    link.click();
    
    // Cleanup
    setTimeout(() => {
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    }, 0);
  } catch (error) {
    console.error('Error generating ZIP:', error);
    throw new Error('Failed to generate ZIP file');
  }
}