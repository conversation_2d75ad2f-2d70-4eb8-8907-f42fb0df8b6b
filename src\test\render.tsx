import { ReactElement } from 'react'
import {
  render,
  RenderOptions,
  screen,
  fireEvent,
  waitFor,
  waitForElementToBeRemoved,
  getByRole,
  getByText,
  getByTestId,
  queryByRole,
  queryByText,
  queryByTestId,
  findByRole,
  findByText,
  findByTestId
} from '@testing-library/react'
import { AllTheProviders } from './providers'

// Custom render function with providers
const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options })

// Export commonly used testing library functions explicitly
export {
  screen,
  fireEvent,
  waitFor,
  waitForElementToBeRemoved,
  getByRole,
  getByText,
  getByTestId,
  queryByRole,
  queryByText,
  queryByTestId,
  findByRole,
  findByText,
  findByTestId
}
export { customRender as render }
