import { ProjectTemplate } from '../store/useAppStore'
import { FileNode } from '../types'

// Helper function to create file nodes
const createFile = (name: string, content?: string): FileNode => ({
  name,
  type: 'file',
  content: content || `// ${name}\n// Generated by ProjectForge\n`,
})

const createFolder = (name: string, children: FileNode[] = []): FileNode => ({
  name,
  type: 'directory',
  children,
})

// Getting Started template - Perfect for beginners
const gettingStartedTemplate: FileNode = createFolder('getting-started-project', [
  createFile('.gitignore', `# Dependencies
node_modules/
.pnp
.pnp.js

# Production
/build
/dist

# Environment variables
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE
.vscode/
.idea/

# OS
.DS_Store
Thumbs.db`),
  createFile('package.json', `{
  "name": "getting-started-project",
  "private": true,
  "version": "1.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
    "preview": "vite preview",
    "test": "vitest"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "lucide-react": "^0.344.0"
  },
  "devDependencies": {
    "@types/react": "^18.2.43",
    "@types/react-dom": "^18.2.17",
    "@typescript-eslint/eslint-plugin": "^6.14.0",
    "@typescript-eslint/parser": "^6.14.0",
    "@vitejs/plugin-react": "^4.2.1",
    "autoprefixer": "^10.4.16",
    "eslint": "^8.55.0",
    "eslint-plugin-react-hooks": "^4.6.0",
    "eslint-plugin-react-refresh": "^0.4.5",
    "postcss": "^8.4.32",
    "tailwindcss": "^3.3.6",
    "typescript": "^5.2.2",
    "vite": "^5.0.8",
    "vitest": "^1.1.0"
  }
}`),
  createFile('vite.config.ts', `import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    open: true
  }
})`),
  createFile('tsconfig.json', `{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}`),
  createFile('tsconfig.node.json', `{
  "compilerOptions": {
    "composite": true,
    "skipLibCheck": true,
    "module": "ESNext",
    "moduleResolution": "bundler",
    "allowSyntheticDefaultImports": true
  },
  "include": ["vite.config.ts"]
}`),
  createFile('tailwind.config.js', `/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
        }
      }
    },
  },
  plugins: [],
}`),
  createFile('postcss.config.js', `export default {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
}`),
  createFile('index.html', `<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Getting Started Project</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>`),
  createFile('README.md', `# Getting Started Project 🚀

Welcome to your new React + TypeScript + Tailwind CSS project! This template provides a solid foundation for building modern web applications.

## 🛠️ What's Included

- **React 18** - Latest version with modern features
- **TypeScript** - Type safety and better developer experience
- **Tailwind CSS** - Utility-first CSS framework
- **Vite** - Fast build tool and development server
- **ESLint** - Code linting for consistent code quality
- **Vitest** - Fast unit testing framework

## 🚀 Getting Started

1. **Install dependencies:**
   \`\`\`bash
   npm install
   \`\`\`

2. **Start development server:**
   \`\`\`bash
   npm run dev
   \`\`\`

3. **Open your browser:**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

\`\`\`
src/
├── components/     # Reusable UI components
├── hooks/         # Custom React hooks
├── utils/         # Utility functions
├── types/         # TypeScript type definitions
├── App.tsx        # Main application component
├── main.tsx       # Application entry point
└── index.css      # Global styles
\`\`\`

## 🧪 Available Scripts

- \`npm run dev\` - Start development server
- \`npm run build\` - Build for production
- \`npm run preview\` - Preview production build
- \`npm run lint\` - Run ESLint
- \`npm run test\` - Run tests

## 🎨 Styling

This project uses Tailwind CSS for styling. You can:

- Use utility classes directly in your JSX
- Customize the theme in \`tailwind.config.js\`
- Add custom CSS in \`src/index.css\`

## 📚 Learn More

- [React Documentation](https://reactjs.org/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Vite Documentation](https://vitejs.dev/)

## 🤝 Contributing

Feel free to submit issues and enhancement requests!

---

**Happy coding!** 🎉`),
  createFolder('public', [
    createFile('vite.svg', `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" class="iconify iconify--logos" width="31.88" height="32" preserveAspectRatio="xMidYMid meet" viewBox="0 0 256 257"><defs><linearGradient id="IconifyId1813088fe1fbc01fb466" x1="-.828%" x2="57.636%" y1="7.652%" y2="78.411%"><stop offset="0%" stop-color="#41D1FF"></stop><stop offset="100%" stop-color="#BD34FE"></stop></linearGradient><linearGradient id="IconifyId1813088fe1fbc01fb467" x1="43.376%" x2="50.316%" y1="2.242%" y2="89.03%"><stop offset="0%" stop-color="#FFEA83"></stop><stop offset="8.333%" stop-color="#FFDD35"></stop><stop offset="100%" stop-color="#FFA800"></stop></linearGradient></defs><path fill="url(#IconifyId1813088fe1fbc01fb466)" d="M255.153 37.938L134.897 252.976c-2.483 4.44-8.862 4.466-11.382.048L.875 37.958c-2.746-4.814 1.371-10.646 6.827-9.67l120.385 21.517a6.537 6.537 0 0 0 2.322-.004l117.867-21.483c5.438-.991 9.574 4.796 6.877 9.62Z"></path><path fill="url(#IconifyId1813088fe1fbc01fb467)" d="M185.432.063L96.44 17.501a3.268 3.268 0 0 0-2.634 3.014l-5.474 92.456a3.268 3.268 0 0 0 3.997 3.378l24.777-5.718c2.318-.535 4.413 1.507 3.936 3.838l-7.361 36.047c-.495 2.426 1.782 4.5 4.151 3.78l15.304-4.649c2.372-.72 4.652 1.36 4.15 3.788l-11.698 56.621c-.732 3.542 3.979 5.473 5.943 2.437l1.313-2.028l72.516-144.72c1.215-2.423-.88-5.186-3.54-4.672l-25.505 4.922c-2.396.462-4.435-1.77-3.759-4.114l16.646-57.705c.677-2.35-1.37-4.583-3.769-4.113Z"></path></svg>`),
  ]),
  createFolder('src', [
    createFile('main.tsx', `import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)`),
    createFile('App.tsx', `import React, { useState } from 'react'
import { Header } from './components/Header'
import { Hero } from './components/Hero'
import { FeatureCard } from './components/FeatureCard'
import { Button } from './components/Button'
import { Code, Palette, Zap, Shield } from 'lucide-react'

function App() {
  const [count, setCount] = useState(0)

  const features = [
    {
      icon: <Code className="h-8 w-8" />,
      title: 'TypeScript Ready',
      description: 'Built with TypeScript for better development experience and type safety.'
    },
    {
      icon: <Palette className="h-8 w-8" />,
      title: 'Tailwind CSS',
      description: 'Utility-first CSS framework for rapid UI development.'
    },
    {
      icon: <Zap className="h-8 w-8" />,
      title: 'Lightning Fast',
      description: 'Powered by Vite for instant hot module replacement and fast builds.'
    },
    {
      icon: <Shield className="h-8 w-8" />,
      title: 'Best Practices',
      description: 'Configured with ESLint, Prettier, and modern development tools.'
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <Header />

      <main className="container mx-auto px-4 py-8">
        <Hero />

        {/* Interactive Counter Section */}
        <section className="my-16 text-center">
          <div className="bg-white rounded-lg shadow-lg p-8 max-w-md mx-auto">
            <h2 className="text-2xl font-bold text-gray-800 mb-4">
              Interactive Counter
            </h2>
            <div className="text-4xl font-bold text-primary-600 mb-6">
              {count}
            </div>
            <div className="flex gap-4 justify-center">
              <Button
                onClick={() => setCount(count - 1)}
                variant="outline"
              >
                Decrease
              </Button>
              <Button
                onClick={() => setCount(count + 1)}
                variant="primary"
              >
                Increase
              </Button>
            </div>
            <Button
              onClick={() => setCount(0)}
              variant="secondary"
              className="mt-4"
            >
              Reset
            </Button>
          </div>
        </section>

        {/* Features Grid */}
        <section className="my-16">
          <h2 className="text-3xl font-bold text-center text-gray-800 mb-12">
            What's Included
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {features.map((feature, index) => (
              <FeatureCard
                key={index}
                icon={feature.icon}
                title={feature.title}
                description={feature.description}
              />
            ))}
          </div>
        </section>

        {/* Getting Started Section */}
        <section className="my-16 bg-white rounded-lg shadow-lg p-8">
          <h2 className="text-3xl font-bold text-center text-gray-800 mb-8">
            Ready to Build Something Amazing?
          </h2>
          <div className="max-w-2xl mx-auto text-center">
            <p className="text-gray-600 mb-6">
              This template gives you everything you need to start building modern React applications.
              Explore the code, customize the components, and make it your own!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="primary" size="lg">
                Start Building
              </Button>
              <Button variant="outline" size="lg">
                View Documentation
              </Button>
            </div>
          </div>
        </section>
      </main>
    </div>
  )
}

export default App`),
    createFile('index.css', `@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
}

@layer components {
  .container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}`),
    createFolder('components', [
      createFile('Header.tsx', `import React from 'react'
import { Github, ExternalLink } from 'lucide-react'

export const Header: React.FC = () => {
  return (
    <header className="bg-white shadow-sm border-b">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">GS</span>
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-800">Getting Started</h1>
              <p className="text-sm text-gray-600">React + TypeScript + Tailwind</p>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <a
              href="https://github.com"
              target="_blank"
              rel="noopener noreferrer"
              className="text-gray-600 hover:text-gray-800 transition-colors"
            >
              <Github className="h-5 w-5" />
            </a>
            <a
              href="https://vitejs.dev"
              target="_blank"
              rel="noopener noreferrer"
              className="text-gray-600 hover:text-gray-800 transition-colors"
            >
              <ExternalLink className="h-5 w-5" />
            </a>
          </div>
        </div>
      </div>
    </header>
  )
}`),
      createFile('Hero.tsx', `import React from 'react'
import { Button } from './Button'
import { ArrowRight, Sparkles } from 'lucide-react'

export const Hero: React.FC = () => {
  return (
    <section className="text-center py-16">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center justify-center mb-6">
          <Sparkles className="h-8 w-8 text-primary-500 mr-2" />
          <span className="text-primary-600 font-semibold">Welcome to your new project!</span>
        </div>

        <h1 className="text-5xl md:text-6xl font-bold text-gray-800 mb-6">
          Build Amazing
          <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
            {' '}React Apps
          </span>
        </h1>

        <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
          This template includes everything you need to start building modern web applications
          with React, TypeScript, and Tailwind CSS. Fast, type-safe, and beautiful.
        </p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button variant="primary" size="lg" className="group">
            Get Started
            <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
          </Button>
          <Button variant="outline" size="lg">
            View Components
          </Button>
        </div>

        <div className="mt-12 text-sm text-gray-500">
          <p>✨ Generated by ProjectForge</p>
        </div>
      </div>
    </section>
  )
}`),
      createFile('Button.tsx', `import React from 'react'
import { cn } from '../utils/cn'

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  children: React.ReactNode
}

export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  className,
  children,
  ...props
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed'

  const variantClasses = {
    primary: 'bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 shadow-lg hover:shadow-xl',
    secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',
    outline: 'border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white focus:ring-primary-500',
    ghost: 'text-gray-600 hover:bg-gray-100 focus:ring-gray-500'
  }

  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  }

  return (
    <button
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        className
      )}
      {...props}
    >
      {children}
    </button>
  )
}`),
      createFile('FeatureCard.tsx', `import React from 'react'

interface FeatureCardProps {
  icon: React.ReactNode
  title: string
  description: string
}

export const FeatureCard: React.FC<FeatureCardProps> = ({ icon, title, description }) => {
  return (
    <div className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-300">
      <div className="text-primary-600 mb-4">
        {icon}
      </div>
      <h3 className="text-xl font-semibold text-gray-800 mb-2">
        {title}
      </h3>
      <p className="text-gray-600">
        {description}
      </p>
    </div>
  )
}`),
    ]),
    createFolder('hooks', [
      createFile('useLocalStorage.ts', `import { useState, useEffect } from 'react'

/**
 * Custom hook for managing localStorage with React state
 * @param key - The localStorage key
 * @param initialValue - Initial value if key doesn't exist
 * @returns [value, setValue] tuple
 */
export function useLocalStorage<T>(key: string, initialValue: T) {
  // Get value from localStorage or use initial value
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key)
      return item ? JSON.parse(item) : initialValue
    } catch (error) {
      console.warn(\`Error reading localStorage key "\${key}":\`, error)
      return initialValue
    }
  })

  // Update localStorage when state changes
  const setValue = (value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value
      setStoredValue(valueToStore)
      window.localStorage.setItem(key, JSON.stringify(valueToStore))
    } catch (error) {
      console.warn(\`Error setting localStorage key "\${key}":\`, error)
    }
  }

  return [storedValue, setValue] as const
}`),
      createFile('useCounter.ts', `import { useState, useCallback } from 'react'

interface UseCounterOptions {
  min?: number
  max?: number
  step?: number
}

/**
 * Custom hook for managing counter state with optional constraints
 * @param initialValue - Starting value for the counter
 * @param options - Optional constraints (min, max, step)
 * @returns Counter state and control functions
 */
export function useCounter(initialValue = 0, options: UseCounterOptions = {}) {
  const { min, max, step = 1 } = options
  const [count, setCount] = useState(initialValue)

  const increment = useCallback(() => {
    setCount(prev => {
      const newValue = prev + step
      return max !== undefined ? Math.min(newValue, max) : newValue
    })
  }, [step, max])

  const decrement = useCallback(() => {
    setCount(prev => {
      const newValue = prev - step
      return min !== undefined ? Math.max(newValue, min) : newValue
    })
  }, [step, min])

  const reset = useCallback(() => {
    setCount(initialValue)
  }, [initialValue])

  const set = useCallback((value: number) => {
    setCount(value)
  }, [])

  return {
    count,
    increment,
    decrement,
    reset,
    set
  }
}`),
    ]),
    createFolder('utils', [
      createFile('cn.ts', `import { type ClassValue, clsx } from 'clsx'

/**
 * Utility function for conditionally joining classNames
 * This is a simplified version - in a real project you might use tailwind-merge
 * @param inputs - Class values to merge
 * @returns Merged className string
 */
export function cn(...inputs: ClassValue[]) {
  return clsx(inputs)
}`),
      createFile('formatters.ts', `/**
 * Format a number as currency
 * @param amount - The amount to format
 * @param currency - Currency code (default: 'USD')
 * @returns Formatted currency string
 */
export function formatCurrency(amount: number, currency = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(amount)
}

/**
 * Format a date in a human-readable way
 * @param date - Date to format
 * @returns Formatted date string
 */
export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(date)
}

/**
 * Format a number with thousand separators
 * @param num - Number to format
 * @returns Formatted number string
 */
export function formatNumber(num: number): string {
  return new Intl.NumberFormat('en-US').format(num)
}

/**
 * Truncate text to a specified length
 * @param text - Text to truncate
 * @param length - Maximum length
 * @returns Truncated text with ellipsis if needed
 */
export function truncateText(text: string, length: number): string {
  if (text.length <= length) return text
  return text.slice(0, length) + '...'
}`),
    ]),
    createFolder('types', [
      createFile('index.ts', `// Common types used throughout the application

export interface User {
  id: string
  name: string
  email: string
  avatar?: string
  createdAt: Date
  updatedAt: Date
}

export interface ApiResponse<T> {
  data: T
  success: boolean
  message?: string
  errors?: string[]
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export type Theme = 'light' | 'dark' | 'system'

export type Status = 'idle' | 'loading' | 'success' | 'error'`),
    ]),
  ]),
])

// Modern React + TypeScript + Vite template
const reactViteTemplate: FileNode = createFolder('react-vite-app', [
  createFile('.gitignore'),
  createFile('package.json'),
  createFile('vite.config.ts'),
  createFile('tsconfig.json'),
  createFile('tsconfig.node.json'),
  createFile('index.html'),
  createFile('README.md'),
  createFolder('public', [
    createFile('vite.svg'),
  ]),
  createFolder('src', [
    createFile('main.tsx'),
    createFile('App.tsx'),
    createFile('App.css'),
    createFile('index.css'),
    createFile('vite-env.d.ts'),
    createFolder('components', [
      createFile('Button.tsx'),
      createFile('Header.tsx'),
    ]),
    createFolder('hooks', [
      createFile('useLocalStorage.ts'),
    ]),
    createFolder('utils', [
      createFile('helpers.ts'),
    ]),
    createFolder('types', [
      createFile('index.ts'),
    ]),
  ]),
])

// Next.js 14 App Router template
const nextjsTemplate: FileNode = createFolder('nextjs-app', [
  createFile('.gitignore'),
  createFile('package.json'),
  createFile('next.config.js'),
  createFile('tsconfig.json'),
  createFile('tailwind.config.js'),
  createFile('postcss.config.js'),
  createFile('README.md'),
  createFolder('app', [
    createFile('layout.tsx'),
    createFile('page.tsx'),
    createFile('globals.css'),
    createFolder('api', [
      createFolder('users', [
        createFile('route.ts'),
      ]),
    ]),
    createFolder('components', [
      createFile('Navigation.tsx'),
      createFile('Footer.tsx'),
    ]),
  ]),
  createFolder('lib', [
    createFile('utils.ts'),
    createFile('db.ts'),
  ]),
  createFolder('public', [
    createFile('next.svg'),
    createFile('vercel.svg'),
  ]),
])

// Express.js + TypeScript API template
const expressApiTemplate: FileNode = createFolder('express-api', [
  createFile('.gitignore'),
  createFile('package.json'),
  createFile('tsconfig.json'),
  createFile('nodemon.json'),
  createFile('README.md'),
  createFile('.env.example'),
  createFolder('src', [
    createFile('app.ts'),
    createFile('server.ts'),
    createFolder('controllers', [
      createFile('userController.ts'),
      createFile('authController.ts'),
    ]),
    createFolder('middleware', [
      createFile('auth.ts'),
      createFile('errorHandler.ts'),
      createFile('validation.ts'),
    ]),
    createFolder('models', [
      createFile('User.ts'),
    ]),
    createFolder('routes', [
      createFile('users.ts'),
      createFile('auth.ts'),
    ]),
    createFolder('utils', [
      createFile('database.ts'),
      createFile('logger.ts'),
    ]),
    createFolder('types', [
      createFile('index.ts'),
    ]),
  ]),
  createFolder('tests', [
    createFile('setup.ts'),
    createFolder('unit', [
      createFile('user.test.ts'),
    ]),
    createFolder('integration', [
      createFile('auth.test.ts'),
    ]),
  ]),
])

// Vue 3 + Composition API template
const vue3Template: FileNode = createFolder('vue3-app', [
  createFile('.gitignore'),
  createFile('package.json'),
  createFile('vite.config.ts'),
  createFile('tsconfig.json'),
  createFile('index.html'),
  createFile('README.md'),
  createFolder('public', [
    createFile('favicon.ico'),
  ]),
  createFolder('src', [
    createFile('main.ts'),
    createFile('App.vue'),
    createFile('style.css'),
    createFolder('components', [
      createFile('HelloWorld.vue'),
      createFile('TheHeader.vue'),
    ]),
    createFolder('composables', [
      createFile('useCounter.ts'),
    ]),
    createFolder('router', [
      createFile('index.ts'),
    ]),
    createFolder('stores', [
      createFile('counter.ts'),
    ]),
    createFolder('views', [
      createFile('HomeView.vue'),
      createFile('AboutView.vue'),
    ]),
  ]),
])

// Python FastAPI template
const fastApiTemplate: FileNode = createFolder('fastapi-app', [
  createFile('.gitignore'),
  createFile('requirements.txt'),
  createFile('pyproject.toml'),
  createFile('README.md'),
  createFile('.env.example'),
  createFile('main.py'),
  createFolder('app', [
    createFile('__init__.py'),
    createFile('main.py'),
    createFolder('api', [
      createFile('__init__.py'),
      createFolder('v1', [
        createFile('__init__.py'),
        createFile('endpoints.py'),
      ]),
    ]),
    createFolder('core', [
      createFile('__init__.py'),
      createFile('config.py'),
      createFile('security.py'),
    ]),
    createFolder('models', [
      createFile('__init__.py'),
      createFile('user.py'),
    ]),
    createFolder('schemas', [
      createFile('__init__.py'),
      createFile('user.py'),
    ]),
    createFolder('crud', [
      createFile('__init__.py'),
      createFile('user.py'),
    ]),
  ]),
  createFolder('tests', [
    createFile('__init__.py'),
    createFile('test_main.py'),
  ]),
])

// Simple Static Website template
const staticWebsiteTemplate: FileNode = createFolder('static-website', [
  createFile('.gitignore', `# Build outputs
dist/
build/

# Dependencies
node_modules/

# Environment variables
.env
.env.local

# IDE
.vscode/
.idea/

# OS
.DS_Store
Thumbs.db`),
  createFile('package.json', `{
  "name": "static-website",
  "version": "1.0.0",
  "description": "A simple static website with HTML, CSS, and JavaScript",
  "main": "index.html",
  "scripts": {
    "dev": "live-server --port=3000 --open=index.html",
    "build": "mkdir -p dist && cp -r *.html *.css *.js assets dist/",
    "preview": "live-server dist --port=3001"
  },
  "devDependencies": {
    "live-server": "^1.2.2"
  },
  "keywords": ["html", "css", "javascript", "static", "beginner"],
  "author": "ProjectForge",
  "license": "MIT"
}`),
  createFile('index.html', `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Static Website</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <h2>MyWebsite</h2>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#home" class="nav-link">Home</a>
                </li>
                <li class="nav-item">
                    <a href="#about" class="nav-link">About</a>
                </li>
                <li class="nav-item">
                    <a href="#services" class="nav-link">Services</a>
                </li>
                <li class="nav-item">
                    <a href="#contact" class="nav-link">Contact</a>
                </li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-content">
            <h1 class="hero-title">Welcome to My Website</h1>
            <p class="hero-subtitle">Building amazing experiences with HTML, CSS, and JavaScript</p>
            <button class="cta-button" onclick="scrollToSection('about')">Learn More</button>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="section">
        <div class="container">
            <h2 class="section-title">About Me</h2>
            <div class="about-content">
                <div class="about-text">
                    <p>Hello! I'm a web developer passionate about creating beautiful and functional websites. This template demonstrates the fundamentals of web development using HTML, CSS, and JavaScript.</p>
                    <p>This project includes responsive design, smooth scrolling, interactive elements, and modern CSS techniques.</p>
                </div>
                <div class="about-image">
                    <div class="placeholder-image">
                        <span>Your Photo Here</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="section bg-light">
        <div class="container">
            <h2 class="section-title">Services</h2>
            <div class="services-grid">
                <div class="service-card">
                    <div class="service-icon">🎨</div>
                    <h3>Web Design</h3>
                    <p>Creating beautiful and user-friendly website designs that engage your audience.</p>
                </div>
                <div class="service-card">
                    <div class="service-icon">💻</div>
                    <h3>Development</h3>
                    <p>Building responsive and interactive websites using modern web technologies.</p>
                </div>
                <div class="service-card">
                    <div class="service-icon">📱</div>
                    <h3>Mobile-First</h3>
                    <p>Ensuring your website looks great and works perfectly on all devices.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="section">
        <div class="container">
            <h2 class="section-title">Get In Touch</h2>
            <div class="contact-content">
                <div class="contact-info">
                    <h3>Contact Information</h3>
                    <div class="contact-item">
                        <strong>Email:</strong> <EMAIL>
                    </div>
                    <div class="contact-item">
                        <strong>Phone:</strong> (*************
                    </div>
                    <div class="contact-item">
                        <strong>Location:</strong> Your City, Country
                    </div>
                </div>
                <form class="contact-form" onsubmit="handleFormSubmit(event)">
                    <div class="form-group">
                        <label for="name">Name</label>
                        <input type="text" id="name" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="email">Email</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    <div class="form-group">
                        <label for="message">Message</label>
                        <textarea id="message" name="message" rows="5" required></textarea>
                    </div>
                    <button type="submit" class="submit-button">Send Message</button>
                </form>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 MyWebsite. Built with HTML, CSS, and JavaScript.</p>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>`),
  createFile('styles.css', `/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    scroll-behavior: smooth;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo h2 {
    color: #3b82f6;
    font-weight: 600;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #3b82f6;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding-top: 80px;
}

.hero-content {
    max-width: 600px;
    padding: 0 20px;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    animation: fadeInUp 1s ease;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    animation: fadeInUp 1s ease 0.2s both;
}

.cta-button {
    background: #fff;
    color: #667eea;
    padding: 15px 30px;
    border: none;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    animation: fadeInUp 1s ease 0.4s both;
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}

/* Sections */
.section {
    padding: 80px 0;
}

.bg-light {
    background: #f8fafc;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 600;
    margin-bottom: 3rem;
    color: #1e293b;
}

/* About Section */
.about-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    align-items: center;
}

.about-text p {
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
    line-height: 1.8;
    color: #64748b;
}

.placeholder-image {
    background: #e2e8f0;
    height: 300px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #64748b;
    font-weight: 500;
}

/* Services Section */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.service-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
}

.service-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.service-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #1e293b;
}

.service-card p {
    color: #64748b;
    line-height: 1.6;
}

/* Contact Section */
.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
}

.contact-info h3 {
    margin-bottom: 1.5rem;
    color: #1e293b;
}

.contact-item {
    margin-bottom: 1rem;
    color: #64748b;
}

.contact-form {
    background: #f8fafc;
    padding: 2rem;
    border-radius: 10px;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #1e293b;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #d1d5db;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3b82f6;
}

.submit-button {
    background: #3b82f6;
    color: white;
    padding: 12px 30px;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease;
}

.submit-button:hover {
    background: #2563eb;
}

/* Footer */
.footer {
    background: #1e293b;
    color: white;
    text-align: center;
    padding: 2rem 0;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0,0,0,0.05);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .about-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .contact-content {
        grid-template-columns: 1fr;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }
}`),
  createFile('script.js', `// Mobile navigation toggle
const hamburger = document.querySelector('.hamburger');
const navMenu = document.querySelector('.nav-menu');

hamburger.addEventListener('click', () => {
    hamburger.classList.toggle('active');
    navMenu.classList.toggle('active');
});

// Close mobile menu when clicking on a link
document.querySelectorAll('.nav-link').forEach(n => n.addEventListener('click', () => {
    hamburger.classList.remove('active');
    navMenu.classList.remove('active');
}));

// Smooth scrolling function
function scrollToSection(sectionId) {
    const element = document.getElementById(sectionId);
    if (element) {
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// Navbar scroll effect
window.addEventListener('scroll', () => {
    const navbar = document.querySelector('.navbar');
    if (window.scrollY > 100) {
        navbar.style.background = 'rgba(255, 255, 255, 0.95)';
        navbar.style.backdropFilter = 'blur(10px)';
    } else {
        navbar.style.background = '#fff';
        navbar.style.backdropFilter = 'none';
    }
});

// Form submission handler
function handleFormSubmit(event) {
    event.preventDefault();

    // Get form data
    const formData = new FormData(event.target);
    const name = formData.get('name');
    const email = formData.get('email');
    const message = formData.get('message');

    // Simple validation
    if (!name || !email || !message) {
        alert('Please fill in all fields');
        return;
    }

    // Simulate form submission
    alert(\`Thank you, \${name}! Your message has been sent. We'll get back to you soon.\`);

    // Reset form
    event.target.reset();
}

// Intersection Observer for animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
        }
    });
}, observerOptions);

// Observe all sections for scroll animations
document.addEventListener('DOMContentLoaded', () => {
    const sections = document.querySelectorAll('.section');
    sections.forEach(section => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(30px)';
        section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(section);
    });
});

// Add some interactive features
document.addEventListener('DOMContentLoaded', () => {
    // Add click effect to service cards
    const serviceCards = document.querySelectorAll('.service-card');
    serviceCards.forEach(card => {
        card.addEventListener('click', () => {
            card.style.transform = 'scale(0.95)';
            setTimeout(() => {
                card.style.transform = 'translateY(-5px)';
            }, 150);
        });
    });

    // Add typing effect to hero title (optional enhancement)
    const heroTitle = document.querySelector('.hero-title');
    const originalText = heroTitle.textContent;
    heroTitle.textContent = '';

    let i = 0;
    const typeWriter = () => {
        if (i < originalText.length) {
            heroTitle.textContent += originalText.charAt(i);
            i++;
            setTimeout(typeWriter, 100);
        }
    };

    // Start typing effect after a short delay
    setTimeout(typeWriter, 1000);
});`),
  createFile('README.md', `# Static Website Template 🌐

A beautiful, responsive static website built with HTML, CSS, and JavaScript. Perfect for beginners learning web development fundamentals!

## 🎯 **Perfect For**
- **Complete beginners** to web development
- **Learning HTML, CSS, and JavaScript** fundamentals
- **Portfolio websites** and personal pages
- **Small business websites**
- **Landing pages** and promotional sites

## ✨ **What You'll Learn**
- **HTML5 Semantic Structure** - Proper document structure and accessibility
- **Modern CSS** - Flexbox, Grid, animations, and responsive design
- **Vanilla JavaScript** - DOM manipulation, event handling, and interactivity
- **Responsive Design** - Mobile-first approach and media queries
- **Web Performance** - Optimized loading and smooth animations

## 🛠️ **Features Included**
- ✅ **Responsive Navigation** with mobile hamburger menu
- ✅ **Hero Section** with animated text and call-to-action
- ✅ **About Section** with grid layout
- ✅ **Services Grid** with hover effects
- ✅ **Contact Form** with validation
- ✅ **Smooth Scrolling** navigation
- ✅ **CSS Animations** and transitions
- ✅ **Mobile-First Design**
- ✅ **Cross-browser Compatible**

## 🚀 **Getting Started**

### Prerequisites
- A web browser (Chrome, Firefox, Safari, Edge)
- A code editor (VS Code recommended)
- Basic understanding of HTML, CSS, and JavaScript

### Installation
1. **Download or clone** this template
2. **Open \`index.html\`** in your web browser
3. **Start editing** the files to customize your website

### Development Server (Optional)
For a better development experience with live reload:

\`\`\`bash
# Install live-server globally
npm install -g live-server

# Or install project dependencies
npm install

# Start development server
npm run dev
\`\`\`

## 📁 **Project Structure**
\`\`\`
static-website/
├── index.html          # Main HTML file
├── styles.css          # All CSS styles
├── script.js           # JavaScript functionality
├── package.json        # Project configuration
└── README.md          # This file
\`\`\`

## 🎨 **Customization Guide**

### Colors
The website uses a modern color palette. Update these CSS variables in \`styles.css\`:
\`\`\`css
:root {
  --primary-color: #3b82f6;
  --secondary-color: #667eea;
  --text-color: #333;
  --light-bg: #f8fafc;
}
\`\`\`

### Content
1. **Update \`index.html\`** with your content
2. **Replace placeholder text** with your information
3. **Add your images** to an \`assets/\` folder
4. **Customize the navigation** links and sections

### Styling
1. **Modify \`styles.css\`** for visual changes
2. **Add new sections** by copying existing patterns
3. **Customize animations** and transitions
4. **Update responsive breakpoints** as needed

## 📱 **Responsive Design**
The template is built mobile-first and includes:
- **Flexible grid layouts** that adapt to screen size
- **Mobile navigation menu** with hamburger toggle
- **Optimized typography** for different devices
- **Touch-friendly buttons** and interactive elements

## 🔧 **Browser Support**
- ✅ Chrome (latest)
- ✅ Firefox (latest)
- ✅ Safari (latest)
- ✅ Edge (latest)
- ✅ Mobile browsers

## 📚 **Learning Resources**
- [MDN Web Docs](https://developer.mozilla.org/) - Comprehensive web development documentation
- [CSS-Tricks](https://css-tricks.com/) - CSS tutorials and guides
- [JavaScript.info](https://javascript.info/) - Modern JavaScript tutorial
- [Can I Use](https://caniuse.com/) - Browser compatibility tables

## 🎯 **Next Steps**
Once you're comfortable with this template, consider:
1. **Adding a CSS framework** like Bootstrap or Tailwind CSS
2. **Learning a JavaScript framework** like React or Vue.js
3. **Adding a build process** with tools like Webpack or Vite
4. **Implementing a backend** with Node.js or Python
5. **Learning about web hosting** and deployment

## 🤝 **Contributing**
Feel free to submit issues and enhancement requests!

## 📄 **License**
This project is open source and available under the [MIT License](LICENSE).

---

**Happy coding!** 🎉 Start building your web development skills with this template!`),
  createFolder('assets', [
    createFile('.gitkeep', '# This folder is for images, fonts, and other assets'),
  ]),
])

// React Todo App template
const todoAppTemplate: FileNode = createFolder('react-todo-app', [
  createFile('.gitignore', `# Dependencies
node_modules/
.pnp
.pnp.js

# Production
/build
/dist

# Environment variables
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE
.vscode/
.idea/

# OS
.DS_Store
Thumbs.db`),
  createFile('package.json', `{
  "name": "react-todo-app",
  "private": true,
  "version": "1.0.0",
  "description": "A beginner-friendly Todo app built with React and TypeScript",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
    "preview": "vite preview",
    "test": "vitest"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "lucide-react": "^0.344.0",
    "uuid": "^9.0.1"
  },
  "devDependencies": {
    "@types/react": "^18.2.43",
    "@types/react-dom": "^18.2.17",
    "@types/uuid": "^9.0.7",
    "@typescript-eslint/eslint-plugin": "^6.14.0",
    "@typescript-eslint/parser": "^6.14.0",
    "@vitejs/plugin-react": "^4.2.1",
    "autoprefixer": "^10.4.16",
    "eslint": "^8.55.0",
    "eslint-plugin-react-hooks": "^4.6.0",
    "eslint-plugin-react-refresh": "^0.4.5",
    "postcss": "^8.4.32",
    "tailwindcss": "^3.3.6",
    "typescript": "^5.2.2",
    "vite": "^5.0.8",
    "vitest": "^1.1.0"
  }
}`),
  createFile('README.md', `# React Todo App 📝

A beautiful, feature-rich Todo application built with React, TypeScript, and Tailwind CSS. Perfect for learning React fundamentals and state management!

## 🎯 **Perfect For**
- **React beginners** learning component-based development
- **Understanding state management** with hooks
- **Learning TypeScript** with React
- **Practicing modern CSS** with Tailwind
- **Building interactive UIs**

## ✨ **Features**
- ✅ **Add, edit, and delete** todos
- ✅ **Mark todos as complete**
- ✅ **Filter todos** (All, Active, Completed)
- ✅ **Local storage persistence**
- ✅ **Responsive design**
- ✅ **Keyboard shortcuts**
- ✅ **Drag and drop** reordering
- ✅ **Dark mode toggle**
- ✅ **Todo statistics**

## 🛠️ **Technologies Used**
- **React 18** - Component-based UI library
- **TypeScript** - Type-safe JavaScript
- **Tailwind CSS** - Utility-first CSS framework
- **Vite** - Fast build tool and dev server
- **Lucide React** - Beautiful icons
- **Local Storage** - Data persistence

## 🚀 **Getting Started**

### Installation
\`\`\`bash
# Install dependencies
npm install

# Start development server
npm run dev

# Open http://localhost:5173
\`\`\`

### Building for Production
\`\`\`bash
# Build the app
npm run build

# Preview the build
npm run preview
\`\`\`

## 📚 **What You'll Learn**

### React Concepts
- **Functional Components** and JSX
- **useState Hook** for state management
- **useEffect Hook** for side effects
- **Custom Hooks** for reusable logic
- **Event Handling** and form management
- **Conditional Rendering**
- **Lists and Keys**
- **Component Composition**

### TypeScript Features
- **Interface definitions** for type safety
- **Type annotations** for props and state
- **Generic types** for reusable components
- **Enum types** for constants
- **Optional properties** and union types

### Modern CSS
- **Tailwind CSS** utility classes
- **Responsive design** with breakpoints
- **CSS Grid** and Flexbox layouts
- **Animations** and transitions
- **Dark mode** implementation

## 🏗️ **Project Structure**
\`\`\`
src/
├── components/
│   ├── TodoApp.tsx         # Main app component
│   ├── TodoForm.tsx        # Add todo form
│   ├── TodoList.tsx        # Todo list container
│   ├── TodoItem.tsx        # Individual todo item
│   ├── TodoFilters.tsx     # Filter buttons
│   ├── TodoStats.tsx       # Statistics display
│   └── ThemeToggle.tsx     # Dark mode toggle
├── hooks/
│   ├── useTodos.ts         # Todo management logic
│   ├── useLocalStorage.ts  # Local storage hook
│   └── useTheme.ts         # Theme management
├── types/
│   └── index.ts            # TypeScript interfaces
├── utils/
│   └── constants.ts        # App constants
├── App.tsx                 # Root component
├── main.tsx               # App entry point
└── index.css              # Global styles
\`\`\`

## 🎨 **Key Components**

### TodoApp
The main component that orchestrates the entire application.

### TodoForm
Handles adding new todos with validation and keyboard shortcuts.

### TodoList
Renders the list of todos with filtering and sorting capabilities.

### TodoItem
Individual todo component with edit, delete, and complete functionality.

### Custom Hooks
- **useTodos**: Manages todo state and operations
- **useLocalStorage**: Persists data to browser storage
- **useTheme**: Handles dark/light mode switching

## 🔧 **Available Scripts**
- \`npm run dev\` - Start development server
- \`npm run build\` - Build for production
- \`npm run preview\` - Preview production build
- \`npm run lint\` - Run ESLint
- \`npm test\` - Run tests

## 🎯 **Learning Challenges**
Try implementing these features to enhance your learning:

1. **Categories/Tags** - Add todo categories
2. **Due Dates** - Add date picker for deadlines
3. **Priority Levels** - High, medium, low priority
4. **Search Functionality** - Filter todos by text
5. **Export/Import** - Save todos to file
6. **Animations** - Add smooth transitions
7. **Keyboard Navigation** - Full keyboard support

## 📱 **Responsive Design**
The app is fully responsive and works great on:
- 📱 Mobile phones (320px+)
- 📱 Tablets (768px+)
- 💻 Desktops (1024px+)

## 🌙 **Dark Mode**
Toggle between light and dark themes with the theme switcher in the header.

## 💾 **Data Persistence**
All todos are automatically saved to browser's local storage, so your data persists between sessions.

## 🤝 **Contributing**
Feel free to submit issues and enhancement requests!

---

**Start building and learning!** 🚀 This todo app covers all the essential React concepts you need to know.`),
])

// Full-stack MERN template
const mernTemplate: FileNode = createFolder('mern-app', [
  createFile('.gitignore'),
  createFile('package.json'),
  createFile('README.md'),
  createFolder('client', [
    createFile('package.json'),
    createFile('vite.config.ts'),
    createFile('tsconfig.json'),
    createFile('index.html'),
    createFolder('src', [
      createFile('main.tsx'),
      createFile('App.tsx'),
      createFolder('components', []),
      createFolder('pages', []),
      createFolder('hooks', []),
      createFolder('services', []),
    ]),
  ]),
  createFolder('server', [
    createFile('package.json'),
    createFile('tsconfig.json'),
    createFolder('src', [
      createFile('app.ts'),
      createFile('server.ts'),
      createFolder('controllers', []),
      createFolder('models', []),
      createFolder('routes', []),
      createFolder('middleware', []),
    ]),
  ]),
])

export const projectTemplates: ProjectTemplate[] = [
  {
    id: 'getting-started',
    name: '🚀 Getting Started Template',
    description: 'Perfect for beginners! Complete React + TypeScript + Tailwind CSS setup with examples, documentation, and best practices. Start here if you\'re new to modern web development.',
    category: 'Frontend',
    tags: ['React', 'TypeScript', 'Tailwind', 'Beginner', 'Tutorial', 'Examples'],
    structure: gettingStartedTemplate,
    popularity: 100,
    lastUpdated: '2024-01-20',
    author: 'ProjectForge Team',
    downloads: 25000,
    difficulty: 'Beginner',
    estimatedTime: '2-4 hours',
    features: ['Interactive Components', 'Modern Styling', 'TypeScript', 'Best Practices', 'Documentation'],
  },
  {
    id: 'static-website',
    name: '🌐 Static Website',
    description: 'Learn web development fundamentals with HTML, CSS, and JavaScript. Includes responsive design, animations, and interactive features. Perfect for absolute beginners!',
    category: 'Frontend',
    tags: ['HTML', 'CSS', 'JavaScript', 'Beginner', 'Responsive', 'Static'],
    structure: staticWebsiteTemplate,
    popularity: 98,
    lastUpdated: '2024-01-20',
    author: 'ProjectForge Team',
    downloads: 22000,
    difficulty: 'Beginner',
    estimatedTime: '3-6 hours',
    features: ['Responsive Design', 'CSS Animations', 'Contact Form', 'Mobile Navigation', 'Smooth Scrolling'],
  },
  {
    id: 'react-todo-app',
    name: '📝 React Todo App',
    description: 'Feature-rich todo application with React hooks, TypeScript, and local storage. Learn state management, event handling, and modern React patterns.',
    category: 'Frontend',
    tags: ['React', 'TypeScript', 'Hooks', 'LocalStorage', 'Beginner', 'Interactive'],
    structure: todoAppTemplate,
    popularity: 95,
    lastUpdated: '2024-01-20',
    author: 'ProjectForge Team',
    downloads: 18500,
    difficulty: 'Beginner',
    estimatedTime: '4-8 hours',
    features: ['CRUD Operations', 'Data Persistence', 'Filtering', 'Dark Mode', 'Responsive UI'],
  },
  {
    id: 'react-vite-ts',
    name: '⚡ React + Vite + TypeScript',
    description: 'Modern React application with Vite, TypeScript, and essential tooling. Fast development with hot reload and optimized builds.',
    category: 'Frontend',
    tags: ['React', 'TypeScript', 'Vite', 'Modern'],
    structure: reactViteTemplate,
    popularity: 95,
    lastUpdated: '2024-01-15',
    author: 'ProjectForge',
    downloads: 15420,
    difficulty: 'Intermediate',
    estimatedTime: '1-2 hours',
    features: ['Hot Reload', 'TypeScript', 'Modern Tooling', 'Optimized Build', 'ESLint'],
  },
  {
    id: 'nextjs-14-app',
    name: '🔥 Next.js 14 App Router',
    description: 'Next.js 14 with App Router, TypeScript, and Tailwind CSS. Full-stack React framework with server-side rendering.',
    category: 'Full-stack',
    tags: ['Next.js', 'React', 'TypeScript', 'Tailwind', 'App Router'],
    structure: nextjsTemplate,
    popularity: 92,
    lastUpdated: '2024-01-10',
    author: 'ProjectForge',
    downloads: 12350,
    difficulty: 'Intermediate',
    estimatedTime: '2-4 hours',
    features: ['App Router', 'Server Components', 'API Routes', 'SSR/SSG', 'File-based Routing'],
  },
  {
    id: 'express-api-ts',
    name: '🚀 Express.js API + TypeScript',
    description: 'RESTful API with Express.js, TypeScript, and comprehensive testing. Learn backend development with modern Node.js.',
    category: 'Backend',
    tags: ['Express.js', 'TypeScript', 'API', 'Testing'],
    structure: expressApiTemplate,
    popularity: 88,
    lastUpdated: '2024-01-12',
    author: 'ProjectForge',
    downloads: 9870,
    difficulty: 'Beginner',
    estimatedTime: '3-6 hours',
    features: ['REST API', 'Authentication', 'Database Integration', 'Testing', 'Error Handling'],
  },
  {
    id: 'vue3-composition',
    name: '💚 Vue 3 + Composition API',
    description: 'Vue 3 application with Composition API, TypeScript, and Pinia. Modern Vue development with reactive state management.',
    category: 'Frontend',
    tags: ['Vue.js', 'TypeScript', 'Composition API', 'Pinia'],
    structure: vue3Template,
    popularity: 85,
    lastUpdated: '2024-01-08',
    author: 'ProjectForge',
    downloads: 7650,
    difficulty: 'Intermediate',
    estimatedTime: '2-4 hours',
    features: ['Composition API', 'State Management', 'TypeScript', 'Vue Router', 'Reactive Data'],
  },
  {
    id: 'fastapi-python',
    name: '🐍 FastAPI + Python',
    description: 'High-performance Python API with FastAPI, async support, and automatic documentation. Perfect for learning modern Python web development.',
    category: 'Backend',
    tags: ['Python', 'FastAPI', 'Async', 'API'],
    structure: fastApiTemplate,
    popularity: 82,
    lastUpdated: '2024-01-05',
    author: 'ProjectForge',
    downloads: 6420,
    difficulty: 'Intermediate',
    estimatedTime: '3-5 hours',
    features: ['Async/Await', 'Auto Documentation', 'Type Hints', 'Database ORM', 'Authentication'],
  },
  {
    id: 'mern-fullstack',
    name: '🔥 MERN Full-stack',
    description: 'Complete MERN stack application with MongoDB, Express, React, and Node.js. Learn full-stack development with modern JavaScript.',
    category: 'Full-stack',
    tags: ['MongoDB', 'Express.js', 'React', 'Node.js', 'Full-stack'],
    structure: mernTemplate,
    popularity: 90,
    lastUpdated: '2024-01-14',
    author: 'ProjectForge',
    downloads: 11200,
    difficulty: 'Intermediate',
    estimatedTime: '1-2 weeks',
    features: ['Full-stack JavaScript', 'Database Integration', 'Authentication', 'API Development', 'Modern Frontend'],
  },
]

export const templateCategories = [
  'All',
  '🚀 Beginner Friendly',
  'Frontend',
  'Backend',
  'Full-stack',
  'Mobile',
  'Desktop',
  'CLI',
  'Library',
]

export const difficultyLevels = [
  'All Levels',
  'Beginner',
  'Intermediate',
  'Advanced',
]

export const popularTags = [
  'Beginner',
  'HTML',
  'CSS',
  'JavaScript',
  'React',
  'TypeScript',
  'Next.js',
  'Vue.js',
  'Express.js',
  'FastAPI',
  'Python',
  'Node.js',
  'MongoDB',
  'PostgreSQL',
  'Tailwind',
  'API',
  'Testing',
  'Modern',
  'Tutorial',
  'Examples',
  'Interactive',
  'Responsive',
  'Static',
]

export const beginnerTemplates = projectTemplates.filter(template =>
  template.difficulty === 'Beginner' || template.tags.includes('Beginner')
)
