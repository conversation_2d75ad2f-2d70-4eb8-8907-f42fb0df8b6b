# ProjectForge 🚀

**AI-Powered Project Structure Generator**

ProjectForge is a modern, intelligent project structure generator that helps developers quickly scaffold new projects with best practices, modern tooling, and AI-powered customization.

![ProjectForge Demo](https://via.placeholder.com/800x400/3B82F6/FFFFFF?text=ProjectForge+Demo)

## ✨ Features

### 🎯 **Smart Project Generation**
- **Template Library**: Curated collection of modern project templates
- **AI Generator**: Powered by Google Gemini AI - describe your project and get a custom structure
- **Getting Started Template**: Perfect for beginners with examples and documentation
- **Custom Structures**: Import your own project structures
- **Real-time Preview**: See your project structure before downloading

### 🎨 **Modern UI/UX**
- **Responsive Design**: Works seamlessly on desktop and mobile
- **Dark/Light Theme**: Automatic theme switching based on system preferences
- **Smooth Animations**: Framer Motion powered interactions
- **Accessibility**: WCAG compliant design

### 📊 **Analytics & Insights**
- **Usage Analytics**: Track your project generation patterns
- **Performance Metrics**: Monitor application performance
- **User Behavior**: Understand how you use different features

### 🔧 **Developer Experience**
- **TypeScript**: Full type safety throughout the application
- **Modern Stack**: React 18, Vite, Tailwind CSS
- **Error Handling**: Comprehensive error boundaries and logging
- **Testing**: Complete test suite with Vitest and Testing Library

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- npm or yarn
- Google Gemini API key (for AI features)

### Installation

```bash
# Clone the repository
git clone https://github.com/your-username/projectforge.git
cd projectforge

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.local
# Edit .env.local and add your Gemini API key
```

### Environment Setup

Create a `.env.local` file with your configuration:

```env
# Required: Google Gemini API key for AI features
VITE_GEMINI_API_KEY=your_gemini_api_key_here

# Optional: Enable/disable AI features
VITE_ENABLE_AI_GENERATION=true

# Optional: Application environment
VITE_APP_ENV=development
```

### Getting Your Gemini API Key

1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with your Google account
3. Click "Create API Key"
4. Copy the generated key and add it to your `.env.local` file

### Development

```bash
# Start development server
npm run dev

# Run tests
npm test

# Run linting
npm run lint
```

### Building for Production

```bash
# Build the application
npm run build

# Preview the build
npm run preview
```

## 🏗️ Architecture

ProjectForge is built with a modern, scalable architecture:

```
src/
├── components/          # React components
│   ├── ui/             # Reusable UI components
│   ├── __tests__/      # Component tests
│   └── ...
├── hooks/              # Custom React hooks
├── store/              # Zustand state management
├── utils/              # Utility functions
├── services/           # API and external services
├── data/               # Static data and templates
├── lib/                # Core utilities
└── test/               # Test utilities and setup
```

### Key Technologies

- **Frontend**: React 18, TypeScript, Vite
- **Styling**: Tailwind CSS, Framer Motion
- **State Management**: Zustand with persistence
- **UI Components**: Radix UI primitives
- **Testing**: Vitest, Testing Library
- **Build Tool**: Vite with optimized bundling

## 📖 Usage Guide

### 1. Getting Started Template 🚀
- **Perfect for beginners**: Complete setup with examples
- **Learn by example**: Interactive components and documentation
- **Best practices**: Modern React + TypeScript + Tailwind CSS
- **Ready to customize**: Well-structured and commented code

### 2. Browse Templates
- Explore our curated template library
- Filter by technology, category, or popularity
- Preview template structures before selection
- One-click generation and download

### 3. AI Generation (Powered by Google Gemini)
- **Natural language input**: Describe your project in plain English
- **Smart recommendations**: AI suggests technologies and features
- **Custom structures**: Generated based on your specific needs
- **Fallback support**: Works even if AI service is unavailable

### 4. Custom Structures
- Paste your own folder structure
- Support for tree format or simple folder/file lists
- Automatic parsing and validation
- Save as custom templates

### 5. Download & Deploy
- Download as ZIP file with complete project structure
- Copy structure to clipboard for quick setup
- One-click deployment to Vercel
- GitHub integration for version control

## 🧪 Testing

We maintain comprehensive test coverage across the application:

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Generate coverage report
npm run test:coverage

# Run tests with UI
npm run test:ui
```

### Test Structure
- **Unit Tests**: Individual component and utility testing
- **Integration Tests**: Feature workflow testing
- **Performance Tests**: Performance monitoring and optimization
- **Accessibility Tests**: WCAG compliance verification

## 🤖 AI Integration

ProjectForge uses Google Gemini AI to generate intelligent project structures based on natural language descriptions.

### Features
- **Smart Structure Generation**: AI analyzes your project description and creates appropriate folder structures
- **Technology Recommendations**: Suggests best practices and modern tools
- **Fallback Support**: Works with basic generation if AI is unavailable
- **Real-time Status**: Shows AI service connection status

### How It Works
1. **Input Processing**: Your project description is analyzed for key requirements
2. **Context Building**: AI considers selected technologies, features, and complexity
3. **Structure Generation**: Creates a realistic project structure with proper file organization
4. **Content Creation**: Generates basic file contents and configuration files
5. **Recommendations**: Provides specific suggestions for your project

### AI Service Status
The application displays the current AI service status:
- 🟢 **Connected**: "Powered by Google Gemini" - Full AI features available
- 🟡 **Checking**: Verifying connection to AI service
- 🔴 **Unavailable**: Using fallback generator (still functional!)

## 🚀 Deployment

### Quick Deploy to Vercel

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/your-username/projectforge&env=VITE_GEMINI_API_KEY&envDescription=Google%20Gemini%20API%20key%20for%20AI%20features&envLink=https://makersuite.google.com/app/apikey)

### Manual Deployment

1. **Fork the repository**
2. **Get a Gemini API key** from [Google AI Studio](https://makersuite.google.com/app/apikey)
3. **Deploy to Vercel**:
   - Connect your GitHub repository
   - Add environment variable: `VITE_GEMINI_API_KEY`
   - Deploy!

### Environment Variables for Production

```env
# Required: Google Gemini API key
VITE_GEMINI_API_KEY=your_gemini_api_key_here

# Optional: Feature flags
VITE_ENABLE_AI_GENERATION=true
VITE_APP_ENV=production
```

For detailed deployment instructions, see [DEPLOYMENT.md](DEPLOYMENT.md).

## 🔧 Configuration

### Environment Variables

Create a `.env.local` file in the root directory:

```env
# Required: Google Gemini AI Configuration
VITE_GEMINI_API_KEY=your_gemini_api_key_here

# Optional: Feature toggles
VITE_ENABLE_AI_GENERATION=true

# Optional: Logging configuration
VITE_ENABLE_REMOTE_LOGGING=false
VITE_LOGGING_ENDPOINT=https://your-logging-service.com/api/logs

# Optional: Analytics configuration
VITE_ENABLE_ANALYTICS=true
VITE_ANALYTICS_ENDPOINT=https://your-analytics-service.com/api/events
```

### Customization

#### Adding New Templates
1. Create template structure in `src/data/templates.ts`
2. Add template metadata (name, description, tags)
3. Include in the templates array

#### Extending AI Generator
1. Modify `src/services/aiService.ts`
2. Add new project types or technologies
3. Update generation logic

## 📊 Performance

ProjectForge is optimized for performance:

- **Bundle Size**: < 500KB gzipped
- **First Contentful Paint**: < 1.5s
- **Largest Contentful Paint**: < 2.5s
- **Cumulative Layout Shift**: < 0.1

### Performance Features
- Code splitting and lazy loading
- Image optimization
- Efficient caching strategies
- Performance monitoring and logging

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Workflow
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

### Code Standards
- TypeScript for type safety
- ESLint + Prettier for code formatting
- Conventional commits for commit messages
- Comprehensive test coverage

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [React](https://reactjs.org/) - UI library
- [Vite](https://vitejs.dev/) - Build tool
- [Tailwind CSS](https://tailwindcss.com/) - Styling
- [Framer Motion](https://www.framer.com/motion/) - Animations
- [Radix UI](https://www.radix-ui.com/) - UI primitives
- [Zustand](https://github.com/pmndrs/zustand) - State management

## 🔧 Troubleshooting

### Common Issues

#### AI Service Not Working
- **Check API Key**: Ensure `VITE_GEMINI_API_KEY` is set correctly
- **Verify Permissions**: Make sure your Gemini API key has proper permissions
- **Check Quota**: Verify you haven't exceeded your API quota
- **Fallback Mode**: The app will still work with basic generation if AI fails

#### Build/Deployment Issues
- **Environment Variables**: Ensure all required env vars are set in production
- **TypeScript Errors**: Run `npm run lint` to check for type errors
- **Dependencies**: Clear `node_modules` and reinstall if needed

#### Template Loading Issues
- **Clear Cache**: Clear browser cache and localStorage
- **Check Network**: Ensure stable internet connection
- **Browser Compatibility**: Use a modern browser (Chrome, Firefox, Safari, Edge)

### Debug Commands

```bash
# Check environment variables
echo $VITE_GEMINI_API_KEY

# Test build locally
npm run build && npm run preview

# Run tests
npm test

# Check for TypeScript errors
npx tsc --noEmit

# Analyze bundle size
npm run build && npx vite-bundle-analyzer dist
```

### Getting Help

If you're still having issues:

1. **Check the logs**: Look for error messages in browser console
2. **Search existing issues**: Check [GitHub Issues](https://github.com/your-username/projectforge/issues)
3. **Create a new issue**: Include error messages, browser info, and steps to reproduce
4. **Join the community**: Get help from other users and contributors

## 📞 Support

- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/projectforge)
- 🐛 Issues: [GitHub Issues](https://github.com/your-username/projectforge/issues)
- 📖 Docs: [Documentation](https://docs.projectforge.dev)
- 🚀 Deployment: [Deployment Guide](DEPLOYMENT.md)

---

**Built with ❤️ by the ProjectForge team**
