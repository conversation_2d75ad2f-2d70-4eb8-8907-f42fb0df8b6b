import { FileNode } from '../../types';

export function getVueTemplate(): FileNode[] {
  return [
    {
      name: 'public',
      type: 'directory',
      children: [
        {
          name: 'index.html',
          type: 'file',
          content: `<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <link rel="icon" href="/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue App</title>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>`
        }
      ]
    },
    {
      name: 'src',
      type: 'directory',
      children: [
        {
          name: 'App.vue',
          type: 'file',
          content: `<script setup>
import HelloWorld from './components/HelloWorld.vue'
</script>

<template>
  <header>
    <img alt="Vue logo" class="logo" src="./assets/logo.svg" width="125" height="125" />
    <div class="wrapper">
      <HelloWorld msg="You did it!" />
    </div>
  </header>

  <main>
    <div class="content">
      <p>
        Welcome to your Vue.js application!
      </p>
    </div>
  </main>
</template>

<style scoped>
header {
  line-height: 1.5;
}

.logo {
  display: block;
  margin: 0 auto 2rem;
}

@media (min-width: 1024px) {
  header {
    display: flex;
    place-items: center;
    padding-right: calc(var(--section-gap) / 2);
  }

  .logo {
    margin: 0 2rem 0 0;
  }

  header .wrapper {
    display: flex;
    place-items: flex-start;
    flex-wrap: wrap;
  }
}
</style>`
        },
        {
          name: 'main.js',
          type: 'file',
          content: `import { createApp } from 'vue'
import App from './App.vue'
import './assets/main.css'

createApp(App).mount('#app')`
        },
        {
          name: 'assets',
          type: 'directory',
          children: [
            {
              name: 'logo.svg',
              type: 'file',
              content: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 261.76 226.69">
  <path fill="#41b883" d="M161.096.001l-30.224 52.35L100.647.002H-.005L130.872 226.69 261.749.001z"/>
  <path fill="#34495e" d="M161.096.001l-30.224 52.35L100.647.002H52.346l78.526 136.01L209.398.001z"/>
</svg>`
            },
            {
              name: 'main.css',
              type: 'file',
              content: `@import './base.css';

#app {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  font-weight: normal;
}

a {
  text-decoration: none;
  color: #00bd7e;
  transition: 0.4s;
}

@media (hover: hover) {
  a:hover {
    background-color: #00bd7e33;
  }
}

@media (min-width: 1024px) {
  body {
    display: flex;
    place-items: center;
  }

  #app {
    padding: 0 2rem;
  }
}`
            },
            {
              name: 'base.css',
              type: 'file',
              content: `/* color palette */
:root {
  --vt-c-white: #ffffff;
  --vt-c-black: #181818;
  --vt-c-text: rgba(60, 60, 60, 0.9);
  --vt-c-text-light: rgba(60, 60, 60, 0.66);
  --color-background: var(--vt-c-white);
  --color-text: var(--vt-c-text);
}

/* semantic color variables for this project */
:root {
  --color-background: var(--vt-c-white);
  --color-text: var(--vt-c-text);
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  position: relative;
  font-weight: normal;
}

body {
  min-height: 100vh;
  color: var(--color-text);
  background: var(--color-background);
  line-height: 1.6;
  font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu,
    Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  font-size: 15px;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}`
            }
          ]
        },
        {
          name: 'components',
          type: 'directory',
          children: [
            {
              name: 'HelloWorld.vue',
              type: 'file',
              content: `<script setup>
defineProps({
  msg: {
    type: String,
    required: true
  }
})
</script>

<template>
  <div class="greetings">
    <h1 class="green">{{ msg }}</h1>
    <h3>
      You've successfully created a project with
      <a href="https://vitejs.dev/" target="_blank" rel="noopener">Vite</a> +
      <a href="https://vuejs.org/" target="_blank" rel="noopener">Vue 3</a>.
    </h3>
  </div>
</template>

<style scoped>
h1 {
  font-weight: 500;
  font-size: 2.6rem;
  top: -10px;
}

h3 {
  font-size: 1.2rem;
}

.greetings h1,
.greetings h3 {
  text-align: center;
}

@media (min-width: 1024px) {
  .greetings h1,
  .greetings h3 {
    text-align: left;
  }
}
</style>`
            }
          ]
        }
      ]
    },
    {
      name: 'vite.config.js',
      type: 'file',
      content: `import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  }
})
`
    },
    {
      name: 'package.json',
      type: 'file',
      content: `{
  "name": "vue-project",
  "version": "0.0.0",
  "private": true,
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview"
  },
  "dependencies": {
    "vue": "^3.3.4"
  },
  "devDependencies": {
    "@vitejs/plugin-vue": "^4.2.3",
    "vite": "^4.3.9"
  }
}`
    },
    {
      name: '.gitignore',
      type: 'file',
      content: `# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
.DS_Store
dist
dist-ssr
coverage
*.local

/cypress/videos/
/cypress/screenshots/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?`
    },
    {
      name: 'README.md',
      type: 'file',
      content: `# Vue 3 Project

This template should help get you started developing with Vue 3 in Vite.

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Project Setup

\`\`\`sh
npm install
\`\`\`

### Compile and Hot-Reload for Development

\`\`\`sh
npm run dev
\`\`\`

### Compile and Minify for Production

\`\`\`sh
npm run build
\`\`\``
    }
  ];
}