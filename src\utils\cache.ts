import { logger } from './logger'

export interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number
  key: string
}

export interface CacheOptions {
  ttl?: number // Time to live in milliseconds
  maxSize?: number // Maximum number of entries
  storage?: 'memory' | 'localStorage' | 'sessionStorage'
  serialize?: boolean // Whether to serialize data for storage
}

export class Cache<T = unknown> {
  private cache: Map<string, CacheEntry<T>> = new Map()
  private options: Required<CacheOptions>

  constructor(options: CacheOptions = {}) {
    this.options = {
      ttl: 5 * 60 * 1000, // 5 minutes default
      maxSize: 100,
      storage: 'memory',
      serialize: true,
      ...options
    }

    // Load from persistent storage if configured
    if (this.options.storage !== 'memory') {
      this.loadFromStorage()
    }

    // Set up cleanup interval
    setInterval(() => this.cleanup(), 60000) // Cleanup every minute
  }

  private getStorageKey(key: string): string {
    return `cache_${key}`
  }

  private loadFromStorage(): void {
    if (this.options.storage === 'memory') return

    try {
      const storage = this.options.storage === 'localStorage' ? localStorage : sessionStorage
      const keys = Object.keys(storage).filter(key => key.startsWith('cache_'))
      
      for (const storageKey of keys) {
        const data = storage.getItem(storageKey)
        if (data) {
          const entry: CacheEntry<T> = JSON.parse(data)
          const key = storageKey.replace('cache_', '')
          
          // Check if entry is still valid
          if (this.isValid(entry)) {
            this.cache.set(key, entry)
          } else {
            storage.removeItem(storageKey)
          }
        }
      }
      
      logger.debug(`Loaded ${this.cache.size} entries from ${this.options.storage}`, undefined, 'cache')
    } catch (error) {
      logger.error('Failed to load cache from storage', error, 'cache')
    }
  }

  private saveToStorage(key: string, entry: CacheEntry<T>): void {
    if (this.options.storage === 'memory') return

    try {
      const storage = this.options.storage === 'localStorage' ? localStorage : sessionStorage
      const storageKey = this.getStorageKey(key)
      storage.setItem(storageKey, JSON.stringify(entry))
    } catch (error) {
      logger.error('Failed to save cache entry to storage', { key, error }, 'cache')
    }
  }

  private removeFromStorage(key: string): void {
    if (this.options.storage === 'memory') return

    try {
      const storage = this.options.storage === 'localStorage' ? localStorage : sessionStorage
      const storageKey = this.getStorageKey(key)
      storage.removeItem(storageKey)
    } catch (error) {
      logger.error('Failed to remove cache entry from storage', { key, error }, 'cache')
    }
  }

  private isValid(entry: CacheEntry<T>): boolean {
    return Date.now() - entry.timestamp < entry.ttl
  }

  private evictOldest(): void {
    let oldestKey: string | null = null
    let oldestTimestamp = Date.now()

    for (const [key, entry] of this.cache.entries()) {
      if (entry.timestamp < oldestTimestamp) {
        oldestTimestamp = entry.timestamp
        oldestKey = key
      }
    }

    if (oldestKey) {
      this.delete(oldestKey)
      logger.debug('Evicted oldest cache entry', { key: oldestKey }, 'cache')
    }
  }

  set(key: string, data: T, customTtl?: number): void {
    // Check size limit
    if (this.cache.size >= this.options.maxSize && !this.cache.has(key)) {
      this.evictOldest()
    }

    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: customTtl || this.options.ttl,
      key
    }

    this.cache.set(key, entry)
    this.saveToStorage(key, entry)
    
    logger.debug('Cache entry set', { key, ttl: entry.ttl }, 'cache')
  }

  get(key: string): T | null {
    const entry = this.cache.get(key)
    
    if (!entry) {
      logger.debug('Cache miss', { key }, 'cache')
      return null
    }

    if (!this.isValid(entry)) {
      this.delete(key)
      logger.debug('Cache entry expired', { key }, 'cache')
      return null
    }

    logger.debug('Cache hit', { key }, 'cache')
    return entry.data
  }

  has(key: string): boolean {
    const entry = this.cache.get(key)
    return entry ? this.isValid(entry) : false
  }

  delete(key: string): boolean {
    const deleted = this.cache.delete(key)
    if (deleted) {
      this.removeFromStorage(key)
      logger.debug('Cache entry deleted', { key }, 'cache')
    }
    return deleted
  }

  clear(): void {
    const size = this.cache.size
    
    // Clear memory cache
    this.cache.clear()
    
    // Clear storage cache
    if (this.options.storage !== 'memory') {
      try {
        const storage = this.options.storage === 'localStorage' ? localStorage : sessionStorage
        const keys = Object.keys(storage).filter(key => key.startsWith('cache_'))
        keys.forEach(key => storage.removeItem(key))
      } catch (error) {
        logger.error('Failed to clear cache from storage', error, 'cache')
      }
    }
    
    logger.info('Cache cleared', { entriesRemoved: size }, 'cache')
  }

  cleanup(): void {
    const expiredKeys: string[] = []

    for (const [key, entry] of this.cache.entries()) {
      if (!this.isValid(entry)) {
        expiredKeys.push(key)
      }
    }

    expiredKeys.forEach(key => this.delete(key))
    
    if (expiredKeys.length > 0) {
      logger.debug('Cache cleanup completed', { 
        expired: expiredKeys.length, 
        remaining: this.cache.size 
      }, 'cache')
    }
  }

  getStats(): {
    size: number
    maxSize: number
    hitRate: number
    entries: Array<{ key: string; age: number; ttl: number }>
  } {
    const entries = Array.from(this.cache.entries()).map(([key, entry]) => ({
      key,
      age: Date.now() - entry.timestamp,
      ttl: entry.ttl
    }))

    return {
      size: this.cache.size,
      maxSize: this.options.maxSize,
      hitRate: 0, // Would need to track hits/misses for accurate calculation
      entries
    }
  }

  // Async wrapper for expensive operations
  async getOrSet<R = T>(
    key: string, 
    factory: () => Promise<R>, 
    customTtl?: number
  ): Promise<R> {
    const cached = this.get(key) as R
    if (cached !== null) {
      return cached
    }

    logger.debug('Cache miss, executing factory function', { key }, 'cache')
    const startTime = performance.now()
    
    try {
      const result = await factory()
      const duration = performance.now() - startTime
      
      this.set(key, result as T, customTtl)
      logger.performance(`Cache factory execution: ${key}`, duration)
      
      return result
    } catch (error) {
      logger.error('Cache factory function failed', { key, error }, 'cache')
      throw error
    }
  }
}

// Global cache instances
export const memoryCache = new Cache({
  storage: 'memory',
  ttl: 5 * 60 * 1000, // 5 minutes
  maxSize: 50
})

export const persistentCache = new Cache({
  storage: 'localStorage',
  ttl: 24 * 60 * 60 * 1000, // 24 hours
  maxSize: 100
})

export const sessionCache = new Cache({
  storage: 'sessionStorage',
  ttl: 60 * 60 * 1000, // 1 hour
  maxSize: 25
})

// Specialized caches for different data types
export const templateCache = new Cache<Record<string, unknown>>({
  storage: 'localStorage',
  ttl: 60 * 60 * 1000, // 1 hour
  maxSize: 20
})

export const projectCache = new Cache<Record<string, unknown>>({
  storage: 'localStorage',
  ttl: 7 * 24 * 60 * 60 * 1000, // 7 days
  maxSize: 10
})

// Cache utilities
export const cacheUtils = {
  // Preload data into cache
  preload: async <T>(
    cache: Cache<T>,
    key: string,
    factory: () => Promise<T>,
    ttl?: number
  ): Promise<void> => {
    if (!cache.has(key)) {
      try {
        const data = await factory()
        cache.set(key, data, ttl)
        logger.debug('Data preloaded into cache', { key }, 'cache')
      } catch (error) {
        logger.error('Failed to preload cache data', { key, error }, 'cache')
      }
    }
  },

  // Invalidate cache entries by pattern
  invalidatePattern: (cache: Cache, pattern: RegExp): void => {
    const stats = cache.getStats()
    const keysToDelete = stats.entries
      .filter(entry => pattern.test(entry.key))
      .map(entry => entry.key)
    
    keysToDelete.forEach(key => cache.delete(key))
    logger.debug('Cache entries invalidated by pattern', { 
      pattern: pattern.toString(), 
      count: keysToDelete.length 
    }, 'cache')
  },

  // Get cache statistics for all caches
  getAllStats: () => ({
    memory: memoryCache.getStats(),
    persistent: persistentCache.getStats(),
    session: sessionCache.getStats(),
    template: templateCache.getStats(),
    project: projectCache.getStats()
  })
}
