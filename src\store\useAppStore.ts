import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { FileNode, Project } from '../types'

export interface ProjectTemplate {
  id: string
  name: string
  description: string
  category: string
  tags: string[]
  structure: FileNode
  popularity: number
  lastUpdated: string
  author: string
  downloads: number
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced'
  estimatedTime: string
  features: string[]
}

export interface ProjectHistory {
  id: string
  project: Project
  createdAt: string
  downloadCount: number
}

export interface AppSettings {
  theme: 'light' | 'dark' | 'system'
  autoSave: boolean
  showFileExtensions: boolean
  defaultProjectName: string
  recentProjectsLimit: number
  enableAnalytics: boolean
}

interface AppState {
  // Current project state
  currentProject: Project | null
  isGenerating: boolean
  mode: 'template' | 'custom' | 'ai'
  
  // Project history and templates
  projectHistory: ProjectHistory[]
  favoriteTemplates: string[]
  customTemplates: ProjectTemplate[]
  
  // UI state
  sidebarOpen: boolean
  activeTab: string
  searchQuery: string
  selectedCategory: string
  
  // Settings
  settings: AppSettings
  
  // Analytics
  analytics: {
    projectsGenerated: number
    templatesUsed: Record<string, number>
    featuresUsed: Record<string, number>
  }
}

interface AppActions {
  // Project actions
  setCurrentProject: (project: Project | null) => void
  setIsGenerating: (isGenerating: boolean) => void
  setMode: (mode: 'template' | 'custom' | 'ai') => void
  
  // History actions
  addToHistory: (project: Project) => void
  removeFromHistory: (id: string) => void
  clearHistory: () => void
  
  // Template actions
  addToFavorites: (templateId: string) => void
  removeFromFavorites: (templateId: string) => void
  addCustomTemplate: (template: ProjectTemplate) => void
  removeCustomTemplate: (id: string) => void
  
  // UI actions
  setSidebarOpen: (open: boolean) => void
  setActiveTab: (tab: string) => void
  setSearchQuery: (query: string) => void
  setSelectedCategory: (category: string) => void
  
  // Settings actions
  updateSettings: (settings: Partial<AppSettings>) => void
  
  // Analytics actions
  incrementProjectsGenerated: () => void
  incrementTemplateUsed: (templateId: string) => void
  incrementFeatureUsed: (feature: string) => void
  
  // Utility actions
  reset: () => void
}

const defaultSettings: AppSettings = {
  theme: 'system',
  autoSave: true,
  showFileExtensions: true,
  defaultProjectName: 'my-project',
  recentProjectsLimit: 10,
  enableAnalytics: true,
}

const initialState: AppState = {
  currentProject: null,
  isGenerating: false,
  mode: 'template',
  projectHistory: [],
  favoriteTemplates: [],
  customTemplates: [],
  sidebarOpen: true,
  activeTab: 'templates',
  searchQuery: '',
  selectedCategory: 'all',
  settings: defaultSettings,
  analytics: {
    projectsGenerated: 0,
    templatesUsed: {},
    featuresUsed: {},
  },
}

export const useAppStore = create<AppState & AppActions>()(
  persist(
    (set, get) => ({
      ...initialState,
      
      // Project actions
      setCurrentProject: (project) => set({ currentProject: project }),
      setIsGenerating: (isGenerating) => set({ isGenerating }),
      setMode: (mode) => set({ mode }),
      
      // History actions
      addToHistory: (project) => {
        const { projectHistory, settings } = get()
        const newEntry: ProjectHistory = {
          id: Math.random().toString(36).substr(2, 9),
          project,
          createdAt: new Date().toISOString(),
          downloadCount: 0,
        }
        
        const updatedHistory = [newEntry, ...projectHistory]
          .slice(0, settings.recentProjectsLimit)
        
        set({ projectHistory: updatedHistory })
      },
      
      removeFromHistory: (id) => {
        const { projectHistory } = get()
        set({ projectHistory: projectHistory.filter(item => item.id !== id) })
      },
      
      clearHistory: () => set({ projectHistory: [] }),
      
      // Template actions
      addToFavorites: (templateId) => {
        const { favoriteTemplates } = get()
        if (!favoriteTemplates.includes(templateId)) {
          set({ favoriteTemplates: [...favoriteTemplates, templateId] })
        }
      },
      
      removeFromFavorites: (templateId) => {
        const { favoriteTemplates } = get()
        set({ favoriteTemplates: favoriteTemplates.filter(id => id !== templateId) })
      },
      
      addCustomTemplate: (template) => {
        const { customTemplates } = get()
        set({ customTemplates: [...customTemplates, template] })
      },
      
      removeCustomTemplate: (id) => {
        const { customTemplates } = get()
        set({ customTemplates: customTemplates.filter(template => template.id !== id) })
      },
      
      // UI actions
      setSidebarOpen: (open) => set({ sidebarOpen: open }),
      setActiveTab: (tab) => set({ activeTab: tab }),
      setSearchQuery: (query) => set({ searchQuery: query }),
      setSelectedCategory: (category) => set({ selectedCategory: category }),
      
      // Settings actions
      updateSettings: (newSettings) => {
        const { settings } = get()
        set({ settings: { ...settings, ...newSettings } })
      },
      
      // Analytics actions
      incrementProjectsGenerated: () => {
        const { analytics } = get()
        set({
          analytics: {
            ...analytics,
            projectsGenerated: analytics.projectsGenerated + 1,
          },
        })
      },
      
      incrementTemplateUsed: (templateId) => {
        const { analytics } = get()
        const current = analytics.templatesUsed[templateId] || 0
        set({
          analytics: {
            ...analytics,
            templatesUsed: {
              ...analytics.templatesUsed,
              [templateId]: current + 1,
            },
          },
        })
      },
      
      incrementFeatureUsed: (feature) => {
        const { analytics } = get()
        const current = analytics.featuresUsed[feature] || 0
        set({
          analytics: {
            ...analytics,
            featuresUsed: {
              ...analytics.featuresUsed,
              [feature]: current + 1,
            },
          },
        })
      },
      
      // Utility actions
      reset: () => set(initialState),
    }),
    {
      name: 'projectforge-storage',
      partialize: (state) => ({
        projectHistory: state.projectHistory,
        favoriteTemplates: state.favoriteTemplates,
        customTemplates: state.customTemplates,
        settings: state.settings,
        analytics: state.analytics,
      }),
    }
  )
)
