# 🔧 Environment Variables Quick Reference

## 📋 Complete List for Vercel

Copy and paste these into your Vercel Dashboard → Settings → Environment Variables:

### 🚨 **REQUIRED** (App won't work without these)

| Variable Name | Value | Environment | Description |
|---------------|-------|-------------|-------------|
| `VITE_GEMINI_API_KEY` | `your_actual_api_key_here` | All | Google Gemini AI API key |
| `NODE_ENV` | `production` | Production | Node environment |
| `VITE_APP_ENV` | `production` | Production | App environment |

### ⚙️ **RECOMMENDED** (For optimal performance)

| Variable Name | Value | Environment | Description |
|---------------|-------|-------------|-------------|
| `VITE_AI_SERVICE_ENABLED` | `true` | All | Enable/disable AI features |
| `VITE_AI_MAX_RETRIES` | `3` | All | Max API retry attempts |
| `VITE_AI_TIMEOUT` | `30000` | All | API timeout in milliseconds |

### 📊 **OPTIONAL** (For analytics and monitoring)

| Variable Name | Value | Environment | Description |
|---------------|-------|-------------|-------------|
| `VITE_APP_URL` | `https://your-app.vercel.app` | Production | Your app URL |
| `VITE_API_BASE_URL` | `https://your-app.vercel.app/api` | Production | API base URL |
| `VITE_ENABLE_ANALYTICS` | `true` | All | Enable analytics |
| `VITE_ANALYTICS_ID` | `your_analytics_id` | All | Analytics tracking ID |
| `VITE_SENTRY_DSN` | `your_sentry_dsn` | All | Error tracking DSN |

## 🎯 **Quick Setup Commands**

### For Vercel CLI:
```bash
vercel env add VITE_GEMINI_API_KEY production
vercel env add NODE_ENV production  
vercel env add VITE_APP_ENV production
vercel env add VITE_AI_SERVICE_ENABLED production
```

### For Local Development (.env.local):
```bash
VITE_GEMINI_API_KEY=your_development_api_key
NODE_ENV=development
VITE_APP_ENV=development
VITE_AI_SERVICE_ENABLED=true
VITE_AI_MAX_RETRIES=3
VITE_AI_TIMEOUT=30000
VITE_APP_URL=http://localhost:5173
VITE_ENABLE_ANALYTICS=false
```

## 🔍 **How to Get API Keys**

### Google Gemini API Key:
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Click "Create API Key"
3. Copy the key (starts with `AIza...`)

### Analytics ID (Google Analytics):
1. Go to [Google Analytics](https://analytics.google.com)
2. Create property
3. Copy Measurement ID (starts with `G-...`)

### Sentry DSN (Error Tracking):
1. Go to [Sentry.io](https://sentry.io)
2. Create project
3. Copy DSN from project settings

## ✅ **Verification Checklist**

After setting up environment variables:

- [ ] `VITE_GEMINI_API_KEY` is set and valid
- [ ] `NODE_ENV=production` for production environment
- [ ] `VITE_AI_SERVICE_ENABLED=true` to enable AI features
- [ ] App builds successfully (`npm run build`)
- [ ] AI generation works in deployed app
- [ ] No console errors in browser dev tools

## 🚨 **Security Notes**

- ✅ **DO**: Use `VITE_` prefix for client-side variables
- ✅ **DO**: Keep API keys secure and never commit to git
- ✅ **DO**: Use different API keys for development/production
- ❌ **DON'T**: Share API keys publicly
- ❌ **DON'T**: Use production keys in development

## 🔧 **Troubleshooting**

### AI Not Working?
1. Check `VITE_GEMINI_API_KEY` is set correctly
2. Verify API key is valid in Google AI Studio
3. Ensure `VITE_AI_SERVICE_ENABLED=true`

### Build Failing?
1. Check all required variables are set
2. Verify `NODE_ENV=production` for production builds
3. Check Vercel build logs for specific errors

### Variables Not Loading?
1. Ensure variables start with `VITE_` prefix
2. Check variables are set for correct environment
3. Redeploy after adding new variables

---

**💡 Tip**: Always test your deployment with a simple AI generation request to ensure everything is working correctly!
